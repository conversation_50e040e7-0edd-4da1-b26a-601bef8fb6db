spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  datasource:
    master:
      typeAliasesPackage: com.jh
      pageType: mysql
      basePackages: com.jh.sys.dao,com.jh.gen.dao,com.jh.basic.dao,com.jh.sme.dao,com.jh.inf
      mapperResources: classpath*:com/jh/*/dao/**/mapper/*.xml
      url: jdbc:mysql://**************:3306/sme-dev?useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&useSSL=false&nullCatalogMeansCurrent=true
      username: develop
      password: ENC(8RbhzMqtmCtlkuvNUYjrmWI/Ciub800Rw9NHmTHOMls2INTII7PQUWadjBJ3koXU)
      driverClassName: com.mysql.cj.jdbc.Driver
      initialSize: 10
      minIdle: 10
      maxActive: 300
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 30000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      socketTimeout: 90000
  #
  #    kingbase:
  #      pageType: postgresql
  #      basePackages: com.jh.cms.dao.kingbase
  #      mapperResources: classpath*:com/jh/*/dao/kingbase/mapper/*.xml
  #      url: **************************************************************************************
  #      username: develop
  #      password: 1nnx6Z#0
  #      driver-class-name: org.postgresql.Driver
  #      initialSize: 5
  #      minIdle: 5
  #      maxActive: 30
  #      maxWait: 60000
  #      timeBetweenEvictionRunsMillis: 60000
  #      minEvictableIdleTimeMillis: 30000

  data:
    #缓存配置
    redis:
      host: **************
      port: 6378
      password: ENC(W+ZhgiJAMtfZ27DvjgTZJ3VRSbs0RtEiVCPrk5VPSOpFrs08Smy+G66v3vCFPUd9)
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms

#project自定义配置
file:
  filePath: http://localhost:9070
  useFileType: MINIO #文件上传模式 MINIO(minio) LOCAL(本地)  ALIYUN(阿里云)
  temp:
    path: d:/tempFile
  local:
    path: d:/localFile
    prefix: /statics
    urlPrefix: ${file.filePath}${file.local.prefix}
  aliyun:
    endpoint: "oss-cn-hangzhou.aliyuncs.com"
    accessKeyId: ENC(KbjQqi/jI5oNNcWFL/VvF3UodGc4ZY0aiCyJ/Wo6TvLt8PIc/fu/BqroFIO+wZzprGDpxZiuo6ggAvM6lqjcFQ==)
    accessKeySecret: ENC(gm5R2IiHAVoBRRPFLHmb0g/6OlPOSwiDuru1vsYLPnmUT2O7tAfH21P5n57XZk1cvYJG/Au3SjHAm2YW7uv6eA==)
    bucketName: "jsb2022321"
    isserver: false #false 通过阿里云地址访问 true 通过服务器读取阿里云文件 是否服务器访问，阿里云有两种访问文件的方式
    #isserver 为true 是domain 应写服务域名
    domain: https://jsb2022321.oss-cn-hangzhou.aliyuncs.com/
    savefolder: blbConfig/ #药店配置文件路径
  minio:
    endpoint: ENC(t2BA55056THRi38KPg3YVPSfk8ZxVx2bkUzwn0jL/78/UlBeCXifIR7rxuvX1BxFw3x1mjgDzLydvc3wrnbe+Q==)
    accessKey: ENC(Y3B4uU7FDAZUwz9spw1d0MC0I+RsAcmPF7BPZljHkyLmN9pXlW19rmVPQjvMsrRc926TMqek8/XQWWDQUllMlg==)
    secretKey: ENC(FaqW2KEl1k5wFiwqvkJWYilyiXh7ImWUJq0lWiVKdccxgEFSmeDuW7e8OsCkPxJglwphCJXtp49+h9krxSJNbKZB3RTo6WNFHCHl3PmRd70=)
    bucketName: fdtem
token:
  expire:
    seconds: 28800


project:
  #钉钉机器人配置 自动发送钉钉消息
#  dingTalk:
#    webHookUrl: ENC(0LKbr5WUAkl+ZknHxMXjJqaybEdiPaKBnF6Q0u59uWVIE8+2rAmSlf7By37ML+TxcKzVYWFFAMvGcXmnTuAjZfYnWXD36YC9sWbHckY4zM5W6FoYe+8gwbUCoJQFpixmkJUY4fsUYIM5BpVvHjb2rCEP/sEa2/yYLalGqgVJyyIfChfdBWAAJ9wS+e1a9EJUVPeYwZ51vrJhoUqMSXu2tA==)
#    webHookPix: ENC(1TRpCd8t443ADo+Zly4b6bpK7QhwReKdB2qN4CgxbYdbCw+G7xGm51csjNaKn3efGGJ79TMfZje9BYE44Subew==)
  roleurlper:
    # 配置角色url权限
    SUPERADMIN:
      - /flow/**
  cors:
    list: #允许跨域访问域名列表
      - '*'
  #swagger程序文档功能
  swagger:
    #是否生产环境，false 是启用   true是关闭
    enabled: false
    title: 浙江金汇数字技术有限公司模板项目
    description: 主要包含菜单字典权限日志文件等
    version: V1.0
springdoc:
  swagger-ui:
    path: /doc.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      display-name: '模板项目'
      paths-to-match: '/**'
      packages-to-scan: com.jh
  default-flat-param-object: true
knife4j:
  #开启knife4j的增强模式
  enable: true
  setting:
    language: zh_CN
  #是否为生产环境进行拒绝访问；ture为关闭knife4j页面访问功能
  production: ${project.swagger.enabled}

ythoauth:
  # 请求Api地址
  #  url: http://************:8066
  url: https://zlzx.zjamr.zj.gov.cn/zsjapp/api/rest/ythAuth
  # 成功页面
  bindingPage: http://localhost:8081/ythSign
  # 错误页面
  errPage: http://localhost:8081/ythSign
  # appName
  #  appName: jhzjglxt
  appName: jhzjzlzx


# 通过质量在线进行政务网法人单点登录
zwwLogin:
  getLoginInfoUrl: https://api.zljweb.com/api/User/GetZwwUser

expert:
  # 企业用户默认配置
  org:
    password: yF7&eR#2%P@
    roleCode: ORG
    userTypeCode: ORG_ADMIN
  # 监管用户默认配置
  supervise:
    password: yF7&eR#2%P@
    roleCode: REGION
    userTypeCode: SUPERVISE
