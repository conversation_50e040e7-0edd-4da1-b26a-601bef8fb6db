//package com.jh.config;
//
//import co.elastic.clients.elasticsearch.ElasticsearchClient;
//import co.elastic.clients.json.jackson.JacksonJsonpMapper;
//import co.elastic.clients.transport.ElasticsearchTransport;
//import co.elastic.clients.transport.rest_client.RestClientTransport;
//import co.elastic.clients.util.ContentType;
//import org.apache.http.HttpHost;
//import org.apache.http.HttpResponseInterceptor;
//import org.apache.http.auth.AuthScope;
//import org.apache.http.auth.UsernamePasswordCredentials;
//import org.apache.http.client.CredentialsProvider;
//import org.apache.http.impl.client.BasicCredentialsProvider;
//import org.apache.http.message.BasicHeader;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.elasticsearch.client.ClientConfiguration;
//import org.springframework.data.elasticsearch.client.elc.ElasticsearchConfiguration;
//import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
//import org.springframework.http.HttpHeaders;
//import org.elasticsearch.client.RestClient;
//
//import java.util.List;
//
//@Configuration
//@EnableElasticsearchRepositories
//public class JHElasticsearchConfiguration extends ElasticsearchConfiguration {
//
//    @Value("${spring.elasticsearch.rest.uris}")
//    private String restUri;
//
//    @Value("${spring.elasticsearch.rest.username}")
//    private String username;
//
//    @Value("${spring.elasticsearch.rest.password}")
//    private String password;
//    @Override
//    public ClientConfiguration clientConfiguration() {
//        return ClientConfiguration.builder() //
//                .connectedTo(restUri) //
//                .build();
//    }
//
//    @Bean
//    public ElasticsearchTransport elasticsearchTransport() {
//        return new RestClientTransport(getRestClient(), new JacksonJsonpMapper());
//    }
////
//    @Bean
//    public ElasticsearchClient elasticsearchClient() {
//        return new ElasticsearchClient(elasticsearchTransport());
//    }
//    @Bean
//    public RestClient getRestClient() {
//        // 解析配置中的URI
//        String[] uriParts = restUri.split(":");
//        String host = uriParts[1].replace("//", "");
//        int port = Integer.parseInt(uriParts[2]);
//
//        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
//        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
//        return RestClient.builder(new HttpHost(host, port))
//                .setHttpClientConfigCallback(httpClientBuilder -> {
//                    httpClientBuilder.disableAuthCaching();
//                    httpClientBuilder.setDefaultHeaders(List.of(
//                            new BasicHeader(
//                                    HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON)));
//                    httpClientBuilder.addInterceptorLast((HttpResponseInterceptor)
//                            (response, context) ->
//                                    response.addHeader("X-Elastic-Product", "Elasticsearch"));
//                    return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
//                }).build();
//    }
//}
