package com.jh.config;


import com.jh.sys.fiter.TokenFilter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import java.util.List;
import java.util.Map;

/**
 * spring security配置
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@ConfigurationProperties(prefix = "project")
public class SecurityConfig  {

    private Map<String, List<String>> roleurlper;

    public Map<String, List<String>> getRoleurlper() {
        return roleurlper;
    }

    public void setRoleurlper(Map<String, List<String>> roleurlper) {
        this.roleurlper = roleurlper;
    }

    @Bean
    public PasswordEncoder bCryptPasswordEncoder() {
        return new MyPasswordEncoder();
    }

    @Bean
    protected SecurityFilterChain securityFilterChain(HttpSecurity http,TokenFilter tokenFilter
            ,AuthenticationEntryPoint authenticationEntryPoint
            ,LogoutSuccessHandler logoutSuccessHandler
            ,AuthenticationFailureHandler authenticationFailureHandler
            ,AuthenticationSuccessHandler authenticationSuccessHandler) throws Exception {

        // 禁用CSRF防护，需谨慎评估安全风险，若应用存在跨站请求伪造风险场景应考虑合适的替代方案
        http.csrf(csrf -> csrf.disable());

        // 设置基于Token的无状态会话管理策略，表明不需要依靠传统的Session来维持认证状态
        http.sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        // 定义需要放行的Swagger相关资源路径数组
        String[] swaggerPer = {"/v2/api-docs/**", "/v3/api-docs/**", "/swagger-ui/**", "/webjars/**","/llm/knowledge/dify/retrieval"};

        // 配置授权规则，放行多种静态资源以及特定接口等
        http.authorizeHttpRequests(authorize -> {
                    authorize
                            // 放行常见的前端页面相关静态资源
                            .requestMatchers("/*.html", "/favicon.ico", "/css/**", "/js/**", "/fonts/**", "/images/**").permitAll()
                            // 放行Swagger相关资源路径
                            .requestMatchers(swaggerPer).permitAll()
                            // 放行其他自定义的静态资源路径和部分接口路径
                            .requestMatchers("/web/statics/**", "/**/**/rest/**", "/common/inf/**", "/web/**", "/ueditor1.4.3/**", "/ws/**", "/static/**").permitAll()
                            // 放行特定的用户相关接口，可根据实际情况调整这些接口路径
                            .requestMatchers("/sys/captcha/getCode",
                                    "/sys/captcha/checkLoginKaptcha", "/sys/user/uppwd", "/sys/user/bindSer", "/sys/user/qrbindSer").permitAll()
                            ;
                    for (String key:roleurlper.keySet()){
                        List<String> urls = roleurlper.get(key);
                        authorize.requestMatchers(urls.toArray(new String[0])).hasAnyRole(key);
                    }
                    authorize// 其余请求都需要进行认证
                            .anyRequest().authenticated();
                }
        );
        // 配置表单登录相关设置
        http.formLogin(formLogin -> formLogin
                // 指定登录页面的路径
                .loginPage("/sys/login").permitAll()
                // 设置认证成功后的处理器
                .successHandler(authenticationSuccessHandler)
                // 设置认证失败后的处理器
                .failureHandler(authenticationFailureHandler)
        );
        // 配置异常处理相关，设置认证入口点处理器
        http.exceptionHandling(exceptionHandling -> exceptionHandling
                .authenticationEntryPoint(authenticationEntryPoint)
        );
        // 配置注销相关设置，指定注销的URL路径以及注销成功后的处理器
        http.logout(logout -> logout
                .logoutUrl("/**/logout").permitAll()
                .logoutSuccessHandler(logoutSuccessHandler)
        );
        // 解决页面在iframe中显示受限问题，但需注意带来的点击劫持等安全风险，后续应有相应安全补充措施
        http.headers(headers -> headers.frameOptions(frameOptions -> frameOptions.disable()));

        // 添加缓存控制相关设置，可进一步完善具体的缓存控制策略配置，比如设置缓存过期时间等，此处仅开启配置入口
        http.headers(headers -> headers.cacheControl(cacheControl -> {
            // 可在此处添加具体的缓存控制指令，如cacheControl.noCache()等，目前仅占位
        }));
        // 配置基本认证（可根据实际需求确定是否保留，如果不需要基本认证功能可考虑移除）
        http.httpBasic(httpBasic -> {
            // 可根据实际需求添加基本认证相关的额外配置，目前仅占位
        });

        // 配置记住我功能，设置记住我对应的Cookie名称和参数名称
        http.rememberMe(rememberMe -> rememberMe
                .rememberMeCookieName("rememberMe")
                .rememberMeParameter("rememberMe")
        );

        // 配置跨域支持（如果项目涉及跨域请求场景），可根据实际需求进一步完善跨域相关配置细节
        http.cors(cors -> {
            // 可在此处添加跨域配置相关的具体内容，如允许的源、请求方法等，目前仅占位
        });

        // 添加自定义的Token过滤器，在用户名和密码认证过滤器之前执行，用于处理基于Token的认证逻辑
        http.addFilterBefore(tokenFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
    // 配置认证管理器，用于处理用户认证相关逻辑
    @Bean
    public AuthenticationProvider authenticationProvider(UserDetailsService userDetailsService) {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(userDetailsService);
        authenticationProvider.setPasswordEncoder(bCryptPasswordEncoder());
        return authenticationProvider;
    }

}
