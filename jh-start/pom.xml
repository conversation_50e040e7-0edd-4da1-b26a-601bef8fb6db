<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jh</groupId>
    <artifactId>jh-start</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>jh-start</name>
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 验证码-->
        <kaptcha.version>2.3.2</kaptcha.version>
    </properties>
    <parent>
        <artifactId>sme-server</artifactId>
        <groupId>com.jh</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>com.jh.aop</groupId>
            <artifactId>jh-aop</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jh.sys</groupId>
                    <artifactId>module-sys</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jh.sys</groupId>
            <artifactId>module-sys</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jh.inf</groupId>
            <artifactId>module-inf</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jh.gen</groupId>
            <artifactId>module-gen</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.jh.constant</groupId>
            <artifactId>jh-constant</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jh.msglistener</groupId>
            <artifactId>jh-redis-msglistener</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jh.ws</groupId>
            <artifactId>module-ws</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jh.sme</groupId>
            <artifactId>module-sme</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>



        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8.6.0</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>sme-server</finalName>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>lib</directory>
                <targetPath>BOOT-INF/lib/</targetPath>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <!-- 复制子模块的资源文件 -->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-dependency-plugin</artifactId>-->
<!--                <version>3.3.0</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>copy-resources</id>-->
<!--                        <phase>prepare-package</phase>-->
<!--                        <goals>-->
<!--                            <goal>unpack-dependencies</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <includeGroupIds>com.jh</includeGroupIds>-->
<!--                            &lt;!&ndash; 包含多种类型资源文件 &ndash;&gt;-->
<!--                            <includes>*.properties,*.yml,*.yaml,*.xml</includes>-->
<!--                            <outputDirectory>${project.build.directory}/classes</outputDirectory>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <configuration>
                        <encoding>utf-8</encoding>
                        <useDefaultDelimiters>true</useDefaultDelimiters>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                            <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                            <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                            <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>