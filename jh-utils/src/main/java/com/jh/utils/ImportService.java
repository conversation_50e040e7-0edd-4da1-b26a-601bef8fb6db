package com.jh.utils;

import com.alibaba.fastjson2.JSONObject;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.ExcelMessageInfo;
import com.jh.common.bean.LoginUser;
import com.jh.common.bean.SysFileInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.common.util.security.UUIDUtils;
import com.jh.constant.RedisConstant;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.sys.bean.SysUpload;
import com.jh.sys.service.FileService;
import com.jh.sys.service.SysUploadService;
import com.jh.sys.service.impl.FileServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ImportService {
    public static final Logger logger = LoggerFactory.getLogger(ImportService.class);
    @Autowired
    private StringRedisTemplate redisTemplate;


    @Value("${file.temp.path}")
    private String tempPath;
    @Value("${file.useFileType}")
    private String fileSource;
    @Autowired
    private FileServiceFactory fileServiceFactory;

    @Autowired
    private SysUploadService sysUploadService;

    /**
     * 消息通知
     * @param appLoginUser 用户信息
     * @param sendContent 消息内容
     * @param messageType 消息类型
     */
    public void notification(LoginUser appLoginUser, String sendContent, ExportRespAnnotation.SocketMessageTypeEnum messageType, String messageTitle, String fileUrl){
        ExcelMessageInfo wsMsg = new ExcelMessageInfo();
        wsMsg.setSendTime(new Date());
        wsMsg.setMessageType(messageType.getValue());
        wsMsg.setMessageTitle(messageTitle);
        wsMsg.setSendUserId("0");
        wsMsg.setDestUserId(appLoginUser.getId());
        wsMsg.setSendContent(sendContent);
        wsMsg.setFileUrl(fileUrl);
        redisTemplate.convertAndSend(RedisConstant.REDIS_TOPIC_WEBSOCKET, JSONObject.toJSONString(wsMsg));
    }

    /**
     * 验证excel
     *
     * @param file
     */
    public void verifyExcel(MultipartFile file) {
        String originalFileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFileName)) {
            throw  new ServiceException("上传文件不能为空");
        }
        String fileExtension = originalFileName.substring(originalFileName.lastIndexOf(".")).toLowerCase();
        if (!".xls".equals(fileExtension) && !".xlsx".equals(fileExtension)) {
            throw  new ServiceException("非法文件格式，请使用.xls或.xlsx格式文件");
        }
    }

    /**
     * 异步方式
     * @param objectList 返回列表
     * @param c          解析实体
     * @param fileName   返回文件名
     * @param file       导入源文件
     * @param modType    模块
     * @param sonMod     子模块
     * @param startTime  开始时间
     * @return 返回结果文件地址
     */
    public  String faultDataAsync(List<?> objectList, Class<?> c, String fileName, String modType, String sonMod, Date startTime,
                                        MultipartFile file, LoginUser appLoginUser) {
        // 结束时间
        Date endTime = new Date();
        String tempFile = tempPath + "/" + UUIDUtils.getUUID() + ".xlsx";
        EasyExcelUtils.exportExcel(objectList, "Sheet1", c, tempFile);
        FileService fileService = fileServiceFactory.getFileService(fileSource);
        try {
            SysFileInfo fileInfo = new SysFileInfo();
            fileInfo.setName(fileName);
            fileInfo = fileService.upload(FileConvertUtils.createFileItem(new File(tempFile)), fileInfo,"importExcelResult");
            // 删除临时文件
            Files.deleteIfExists(Paths.get(tempFile));
            String returnUrl = fileInfo.getUrl();

            SysFileInfo fileInfo2 = new SysFileInfo();
            try {
                fileInfo2 = fileService.upload(file, fileInfo2,"importExcel");
                logger.info("saveSysUpload的fileInfo：{}",fileInfo2);
            } catch (Exception e) {
                logger.error("保存导入记录到我的导入错误",e);
            }
            SysFileInfo finalFileInfo = fileInfo2;
            logger.info("saveSysUpload的fileInfo2：{}",fileInfo2);
            logger.info("saveSysUpload的finalFileInfo：{}",finalFileInfo);
            saveSysUpload(objectList, modType, sonMod, startTime, endTime, finalFileInfo, returnUrl, appLoginUser);
            return fileInfo.getUrl();
        } catch (Exception e) {
            logger.error("导入失败{}", e);
        }
        return null;
    }

    /**
     * 保存导入记录到我的导入
     *
     * @param objectList    结果数据
     * @param modType       模块
     * @param sonMod        子模块
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param fileInfo      导入源文件
     * @param returnFileUrl 返回结果文件地址
     * @param appLoginUser 用户信息
     */
    private void saveSysUpload(List<?> objectList, String modType, String sonMod, Date startTime, Date endTime,
                                      SysFileInfo fileInfo, String returnFileUrl,LoginUser appLoginUser){
        logger.info("saveSysUpload的fileInfo：{}",fileInfo);
        // 总行数
        int totalNum = objectList.size();
        // 成功行数
        List<JSONObject> jsonObjectList = objectList.stream()
                .map(obj -> (JSONObject) JSONObject.parseObject(JSONObject.toJSONString(obj)))
                .filter(e -> (e.get("importSituation").equals(ImportStatusEnum.IMPORT_SUCCESS.getName())
                        || e.get("importSituation").equals(ImportStatusEnum.COVER_IMPORT_SUCCESS.getName())
                        || e.get("importSituation").equals(ImportStatusEnum.UNTREATED_SUCCESS.getName())
                        || e.get("importSituation").equals(ImportStatusEnum.NO_COVER_IMPORT_SUCCESS.getName())
                        || e.get("importSituation").equals(ImportStatusEnum.UNTREATED_SUCCESS_ADD.getName())
                        || e.get("importSituation").equals(ImportStatusEnum.IMPORT_COVER_IMPORT_SUCCESS.getName()))
                ).collect(Collectors.toList());
        int successNum = jsonObjectList.size();

        SysUpload sysUpload = new SysUpload();
        // 模块
        sysUpload.setModType(modType);
        sysUpload.setSonMod(sonMod);
        // 触发时间
        sysUpload.setUploadTime(startTime);
        // 结束时间
        sysUpload.setGreTime(endTime);
        // 上传文件地址
        sysUpload.setUploadFileUrl(fileInfo.getUrl());
        logger.info("上传文件地址：{}", fileInfo.getUrl());
        // 上传文件名
        sysUpload.setUploadFileName(fileInfo.getName());
        logger.info("上传文件名：{}", fileInfo.getName());
        // 返回文件地址
        sysUpload.setReturnFileUrl(returnFileUrl);
        // 总行数
        sysUpload.setTotalNum(totalNum);
        // 成功行数
        sysUpload.setSuccessNum(successNum);
        // 失败行数
        sysUpload.setErrorNum(totalNum-successNum);
        sysUpload.setUserId(appLoginUser.getId());
        sysUploadService.saveOrUpdateSysUpload(sysUpload);
    }
}
