package com.jh.constant.enums;

/**
 * 流程审批枚举
 * <AUTHOR>
 * @date 2025-05-22
 *
 */
public enum FlowApproveEnum {
    APPROVE("APPROVE","审批通过"),
    REJECT("REJECT","驳回"),
    RETURN("RETURN","退回");

    private final String name;
    private final String code;
    FlowApproveEnum(String code,String name) {
        this.code = code;
        this.name = name;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
}
