package com.jh.constant.enums;

/**
 * 导入状态
 */
public enum ImportStatusEnum {
    IMPORT_SUCCESS("导入成功"),
    COVER_IMPORT_SUCCESS("覆盖导入成功"),
    NO_COVER_IMPORT_SUCCESS("不覆盖导入成功"),
    IMPORT_COVER_IMPORT_SUCCESS("未覆盖处理"),
    UNTREATED_SUCCESS("数据已存在，未处理"),
    UNTREATED_SUCCESS_ADD("数据已存在，增量导入"),
    ERROR("导入失败")
    ;

    private String name;
    ImportStatusEnum(String name){
        this.name=name;
    }

    public String getName() {
        return name;
    }

    private void setName(String name) {
        this.name = name;
    }
}
