package com.jh.gen.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 代码生成业务表字段
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:26
 */
@Table(name = "GEN_TABLE_COLUMN")
@Schema(description = "代码生成业务表字段")
public class GenTableColumn extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "TABLE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "归属表编号")
    private String tableId;

    @Column(name = "COLUMN_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "列名称")
    private String columnName;

    @Column(name = "COLUMN_COMMENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "列描述")
    private String columnComment;

    @Column(name = "COLUMN_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "列类型")
    private String columnType;

    @Column(name = "JAVA_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "JAVA类型")
    private String javaType;

    @Column(name = "JAVA_FIELD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "JAVA字段名")
    private String javaField;

    @Column(name = "IS_PK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "是否主键（1是）")
    private String isPk;

    @Column(name = "IS_INCREMENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "是否自增（1是）")
    private String isIncrement;

    @Column(name = "IS_REQUIRED")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "是否必填（1是）")
    private String isRequired;

    @Column(name = "IS_INSERT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "是否为插入字段（1是）")
    private String isInsert;

    @Column(name = "IS_EDIT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "是否编辑字段（1是）")
    private String isEdit;

    @Column(name = "IS_LIST")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "是否列表字段（1是）")
    private String isList;

    @Column(name = "IS_QUERY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "是否查询字段（1是）")
    private String isQuery;

    @Column(name = "QUERY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "查询方式（等于、不等于、大于、小于、范围）")
    private String queryType;

    @Column(name = "HTML_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）")
    private String htmlType;

    @Column(name = "DICT_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "字典类型")
    private String dictType;

    @Column(name = "SORT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "排序")
    private Integer sort;

    /**
     * GET 归属表编号
     *
     * @return tableId
     */
    public String getTableId() {
        return tableId;
    }

    /**
     * SET 归属表编号
     *
     * @param tableId
     */
    public void setTableId(String tableId) {
        this.tableId = tableId == null ? null : tableId.trim();
    }

    /**
     * GET 列名称
     *
     * @return columnName
     */
    public String getColumnName() {
        return columnName;
    }

    /**
     * SET 列名称
     *
     * @param columnName
     */
    public void setColumnName(String columnName) {
        this.columnName = columnName == null ? null : columnName.trim();
    }

    /**
     * GET 列描述
     *
     * @return columnComment
     */
    public String getColumnComment() {
        return columnComment;
    }

    /**
     * SET 列描述
     *
     * @param columnComment
     */
    public void setColumnComment(String columnComment) {
        this.columnComment = columnComment == null ? null : columnComment.trim();
    }

    /**
     * GET 列类型
     *
     * @return columnType
     */
    public String getColumnType() {
        return columnType;
    }

    /**
     * SET 列类型
     *
     * @param columnType
     */
    public void setColumnType(String columnType) {
        this.columnType = columnType == null ? null : columnType.trim();
    }

    /**
     * GET JAVA类型
     *
     * @return javaType
     */
    public String getJavaType() {
        return javaType;
    }

    /**
     * SET JAVA类型
     *
     * @param javaType
     */
    public void setJavaType(String javaType) {
        this.javaType = javaType == null ? null : javaType.trim();
    }

    /**
     * GET JAVA字段名
     *
     * @return javaField
     */
    public String getJavaField() {
        return javaField;
    }

    /**
     * SET JAVA字段名
     *
     * @param javaField
     */
    public void setJavaField(String javaField) {
        this.javaField = javaField == null ? null : javaField.trim();
    }

    /**
     * GET 是否主键（1是）
     *
     * @return isPk
     */
    public String getIsPk() {
        return isPk;
    }

    /**
     * SET 是否主键（1是）
     *
     * @param isPk
     */
    public void setIsPk(String isPk) {
        this.isPk = isPk == null ? null : isPk.trim();
    }

    /**
     * GET 是否自增（1是）
     *
     * @return isIncrement
     */
    public String getIsIncrement() {
        return isIncrement;
    }

    /**
     * SET 是否自增（1是）
     *
     * @param isIncrement
     */
    public void setIsIncrement(String isIncrement) {
        this.isIncrement = isIncrement == null ? null : isIncrement.trim();
    }

    /**
     * GET 是否必填（1是）
     *
     * @return isRequired
     */
    public String getIsRequired() {
        return isRequired;
    }

    /**
     * SET 是否必填（1是）
     *
     * @param isRequired
     */
    public void setIsRequired(String isRequired) {
        this.isRequired = isRequired == null ? null : isRequired.trim();
    }

    /**
     * GET 是否为插入字段（1是）
     *
     * @return isInsert
     */
    public String getIsInsert() {
        return isInsert;
    }

    /**
     * SET 是否为插入字段（1是）
     *
     * @param isInsert
     */
    public void setIsInsert(String isInsert) {
        this.isInsert = isInsert == null ? null : isInsert.trim();
    }

    /**
     * GET 是否编辑字段（1是）
     *
     * @return isEdit
     */
    public String getIsEdit() {
        return isEdit;
    }

    /**
     * SET 是否编辑字段（1是）
     *
     * @param isEdit
     */
    public void setIsEdit(String isEdit) {
        this.isEdit = isEdit == null ? null : isEdit.trim();
    }

    /**
     * GET 是否列表字段（1是）
     *
     * @return isList
     */
    public String getIsList() {
        return isList;
    }

    /**
     * SET 是否列表字段（1是）
     *
     * @param isList
     */
    public void setIsList(String isList) {
        this.isList = isList == null ? null : isList.trim();
    }

    /**
     * GET 是否查询字段（1是）
     *
     * @return isQuery
     */
    public String getIsQuery() {
        return isQuery;
    }

    /**
     * SET 是否查询字段（1是）
     *
     * @param isQuery
     */
    public void setIsQuery(String isQuery) {
        this.isQuery = isQuery == null ? null : isQuery.trim();
    }

    /**
     * GET 查询方式（等于、不等于、大于、小于、范围）
     *
     * @return queryType
     */
    public String getQueryType() {
        return queryType;
    }

    /**
     * SET 查询方式（等于、不等于、大于、小于、范围）
     *
     * @param queryType
     */
    public void setQueryType(String queryType) {
        this.queryType = queryType == null ? null : queryType.trim();
    }

    /**
     * GET 显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）
     *
     * @return htmlType
     */
    public String getHtmlType() {
        return htmlType;
    }

    /**
     * SET 显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）
     *
     * @param htmlType
     */
    public void setHtmlType(String htmlType) {
        this.htmlType = htmlType == null ? null : htmlType.trim();
    }

    /**
     * GET 字典类型
     *
     * @return dictType
     */
    public String getDictType() {
        return dictType;
    }

    /**
     * SET 字典类型
     *
     * @param dictType
     */
    public void setDictType(String dictType) {
        this.dictType = dictType == null ? null : dictType.trim();
    }

    /**
     * GET 排序
     *
     * @return sort
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * SET 排序
     *
     * @param sort
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}