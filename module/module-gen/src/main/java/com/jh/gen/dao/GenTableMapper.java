package com.jh.gen.dao;


import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.gen.bean.GenTable;

import java.util.List;

/**
 * 代码生成业务表
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:19
 */
public interface GenTableMapper extends BaseInfoMapper<GenTable> {

    /**
     * 查询据库列表
     *
     * @param genTable 业务信息
     * @return 数据库表集合
     */
    List<GenTable> findDbTableList(GenTable genTable);

    /**
     * 查询据库列表
     *
     * @param tableNames 表名称组
     * @return 数据库表集合
     */
    List<GenTable> findDbTableListByNames(String[] tableNames);

}
