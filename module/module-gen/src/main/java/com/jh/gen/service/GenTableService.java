package com.jh.gen.service;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jh.gen.bean.GenTable;
import com.jh.gen.bean.vo.GenTableVo;

import java.util.List;
import java.util.Map;

/**
 * 代码生成业务表
 *
 * <AUTHOR>
 * @date 2021-10-15 09:59:19
 */
public interface GenTableService {

    /**
     * @param genTable 代码生成业务表对象
     * @return String 代码生成业务表ID
     * 保存或更新代码生成业务表
     * <AUTHOR>
     */
    String saveOrUpdateGenTable(GenTable genTable);

    /**
     * @param id void 代码生成业务表ID
     *           删除代码生成业务表
     * <AUTHOR>
     */
    void deleteGenTable(String id);

    /**
     * @param id
     * @return GenTable
     * 查询代码生成业务表详情
     * <AUTHOR>
     */
    GenTable findById(String id);

    /**
     * @param genTableVo
     * @return PageInfo<GenTable>
     * 分页查询代码生成业务表
     * <AUTHOR>
     */
    PageInfo<GenTable> findPageByQuery(GenTableVo genTableVo);

    /**
     * 查询据库列表
     *
     * @param genTable 业务信息
     * @return 数据库表集合
     */
    PageInfo<GenTable> findDbTableList(GenTableVo genTable);

    /**
     * 查询据库列表
     *
     * @param tableNames 表名称组
     * @param dataSource 数据源
     * @return 数据库表集合
     */
    List<GenTable> findDbTableListByNames(String[] tableNames,String dataSource,String type);

    /**
     * 导入表结构
     *
     * @param tableList 导入表列表
     */
    void importGenTable(List<GenTable> tableList, String operName,String dataSource,String type);

    /**
     * 预览代码
     *
     * @param tableId 表编号
     * @return 预览数据列表
     */
    Map<String, String> previewCode(String tableId);

    /**
     * 生成代码（下载方式）
     *
     * @param tableName 表名称
     * @return 数据
     */
    byte[] downloadCode(String tableName);

    /**
     * 生成代码（自定义路径）
     *
     * @param tableName 表名称
     * @return 数据
     */
    void generatorCode(String tableName);

    /**
     * 同步数据库
     *
     * @param tableName 表名称
     */
    void synchDb(String tableName);

    /**
     * 批量生成代码（下载方式）
     *
     * @param tableNames 表数组
     * @return 数据
     */
    byte[] downloadCode(String[] tableNames);

    /**
     * 修改保存参数校验
     *
     * @param genTable 业务信息
     */
    void validateEdit(GenTableVo genTable);

    List<GenTable> findAll();

    void deleteGenTableByIds(String[] tableIds);

    /**
     * 数据源列表
     * @return
     */
    List<JSONObject> dbSourceList();
}
