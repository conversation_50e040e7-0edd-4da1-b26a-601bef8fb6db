package com.jh.gen.controller;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.common.util.txt.Convert;
import com.jh.gen.bean.GenTable;
import com.jh.gen.bean.GenTableColumn;
import com.jh.gen.bean.vo.GenTableVo;
import com.jh.gen.service.GenTableColumnService;
import com.jh.gen.service.GenTableService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代码生成 操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gen/code")
public class GenController extends BaseController {
    private static final String prefix = "btn:gen:code:";
    @Autowired
    private GenTableService genTableService;
    @Autowired
    private GenTableColumnService genTableColumnService;

    /**
     * 查询代码生成列表
     */
    @PostMapping("/list")
      @Operation(summary="分页查询")
    @PreAuthorize("hasAuthority('" + prefix + "list')")
    public RestApiResponse<?> genList(@RequestBody GenTableVo genTable) {
        PageInfo<GenTable> list = genTableService.findPageByQuery(genTable);
        return RestApiResponse.ok(list);
    }

    /**
     * 修改代码生成业务
     */
    @PreAuthorize("hasAuthority('" + prefix + "query')")
    @GetMapping(value = "/find/{talbleId}")
    public RestApiResponse<?> getInfo(@PathVariable("talbleId") String talbleId) {
        GenTable table = genTableService.findById(talbleId);
        List<GenTable> tables = genTableService.findAll();
        List<GenTableColumn> list = genTableColumnService.findGenTableColumnListByTableId(talbleId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("info", table);
        map.put("rows", list);
        map.put("tables", tables);
        return RestApiResponse.ok(map);
    }

    @PreAuthorize("hasAuthority('" + prefix + "dblist')")
    @PostMapping("/db/source/list")
    public RestApiResponse<?> dbSourceList() {
        List<JSONObject> list = genTableService.dbSourceList();
        return RestApiResponse.ok(list);
    }

    /**
     * 查询数据库列表
     */
    @PreAuthorize("hasAuthority('" + prefix + "dblist')")
    @PostMapping("/db/list")
    public RestApiResponse<?> dataList(@RequestBody GenTableVo genTable) {
        PageInfo<GenTable> list = genTableService.findDbTableList(genTable);
        return RestApiResponse.ok(list);
    }

    /**
     * 导入表结构（保存）
     */
    @PreAuthorize("hasAuthority('" + prefix + "importTable')")
    @SystemLogAnnotation(type = "代码生成", value = "导入表结构（保存）")
    @PostMapping("/importTable")
    public RestApiResponse<?> importTableSave(@RequestParam("tables") String tables,@RequestParam("dataSource") String dataSource
            ,@RequestParam("type") String type) {
        String[] tableNames = Convert.toStrArray(tables);
        // 查询表信息
        List<GenTable> tableList = genTableService.findDbTableListByNames(tableNames,dataSource,type);
        genTableService.importGenTable(tableList, getCurrentUserName(),dataSource,type);
        return RestApiResponse.ok();
    }

    /**
     * 修改保存代码生成业务
     */
    @PreAuthorize("hasAuthority('" + prefix + "edit')")
    @SystemLogAnnotation(type = "代码生成", value = "修改保存代码生成业务")
    @PutMapping
    public RestApiResponse<?> editSave(@Validated @RequestBody GenTableVo genTable) {
        String options = JSONObject.toJSONString(genTable.getParams());
        genTable.setOptions(options);
        genTableService.validateEdit(genTable);
        genTableService.saveOrUpdateGenTable(genTable);
        return RestApiResponse.ok();
    }

    /**
     * 删除代码生成
     */
    @PreAuthorize("hasAuthority('" + prefix + "remove')")
    @SystemLogAnnotation(type = "代码生成", value = "删除代码生成")
    @DeleteMapping("/{tableIds}")
    public RestApiResponse<?> remove(@PathVariable("tableIds") String[] tableIds) {
        genTableService.deleteGenTableByIds(tableIds);
        return RestApiResponse.ok();
    }

    /**
     * 预览代码
     */
    @PreAuthorize("hasAuthority('" + prefix + "preview')")
    @GetMapping("/preview/{tableId}")
    public RestApiResponse<?> preview(@PathVariable("tableId") String tableId) {
        Map<String, String> dataMap = genTableService.previewCode(tableId);
        return RestApiResponse.ok(dataMap);
    }

    /**
     * 生成代码（下载方式）
     */
    @PreAuthorize("hasAuthority('" + prefix + "code')")
    @SystemLogAnnotation(type = "代码生成", value = "生成代码（下载方式）")
    @GetMapping("/download/{tableName}")
    public void download(HttpServletResponse response, @PathVariable("tableName") String tableName) throws IOException {
        byte[] data = genTableService.downloadCode(tableName);
        genCode(response, data);
    }

    /**
     * 生成代码（自定义路径）
     */
    @PreAuthorize("hasAuthority('" + prefix + "gencode')")
    @SystemLogAnnotation(type = "代码生成", value = "生成代码（自定义路径）")
    @GetMapping("/genCode/{tableName}")
    public RestApiResponse<?> genCode(@PathVariable("tableName") String tableName) {
        genTableService.generatorCode(tableName);
        return RestApiResponse.ok();
    }

    /**
     * 同步数据库
     */
    @PreAuthorize("hasAuthority('" + prefix + "synchDb')")
    @SystemLogAnnotation(type = "代码生成", value = "同步数据库")
    @GetMapping("/synchDb/{tableName}")
    public RestApiResponse<?> synchDb(@PathVariable("tableName") String tableName) {
        genTableService.synchDb(tableName);
        return RestApiResponse.ok();
    }

    /**
     * 批量生成代码
     */
    @PreAuthorize("hasAuthority('" + prefix + "batchGen')")
    @SystemLogAnnotation(type = "代码生成", value = "批量生成代码")
    @GetMapping("/batchGenCode")
    public void batchGenCode(@RequestParam("tables") String tables) throws IOException {
        String[] tableNames = Convert.toStrArray(tables);
        byte[] data = genTableService.downloadCode(tableNames);
        genCode(response, data);
    }

    /**
     * 生成zip文件
     */
    private void genCode(HttpServletResponse response, byte[] data) throws IOException {
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=\"lscms.zip\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }
}