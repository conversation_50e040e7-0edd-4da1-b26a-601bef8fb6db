package ${packageName}.${moduleName}.controller;

import java.util.List;
import com.jh.common.annotation.ExportRespAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import ${packageName}.${moduleName}.bean.${ClassName};
import ${packageName}.${moduleName}.bean.vo.${ClassName}Vo;
import ${packageName}.common.bean.RestApiResponse;
import ${packageName}.common.annotation.RepeatSubAnnotation;
import ${packageName}.common.annotation.SystemLogAnnotation;
import ${packageName}.common.controller.BaseController;
import ${packageName}.${moduleName}.service.${ClassName}Service;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * ${functionName}Controller
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${moduleName}/${businessName}")
@Tag(name="${functionName}")
public class ${ClassName}Controller extends BaseController{
    @Autowired
    private ${ClassName}Service ${className}Service;

	private static final String PER_PREFIX = "btn:${permissionPrefix}:";
	
	/**
	 * 新增${functionName}
	 *@param ${className} ${functionName}数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@Operation(summary="新增${functionName}")
	@SystemLogAnnotation(type = "${functionName}",value = "新增${functionName}")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> save${ClassName}(@RequestBody ${ClassName} ${className}) {
		String id = ${className}Service.saveOrUpdate${ClassName}(${className});
		return RestApiResponse.ok(id);
	}
	
	/**
	 * 修改${functionName}
	 *@param ${className} ${functionName}数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@Operation(summary="修改${functionName}")
	@SystemLogAnnotation(type = "${functionName}",value = "修改${functionName}")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> update${ClassName}(@RequestBody ${ClassName} ${className}) {
		String id = ${className}Service.saveOrUpdate${ClassName}(${className});
		return RestApiResponse.ok(id);
	}
	
	/**
	 * 批量删除${functionName}(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@Operation(summary="批量删除${functionName}")
	@SystemLogAnnotation(type = "${functionName}",value = "批量删除${functionName}")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> delete${ClassName}(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		${className}Service.delete${ClassName}(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 * 查询${functionName}详情
	 *@param id
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@GetMapping("/findById")
	@Operation(summary="查询${functionName}详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		${ClassName}  ${className}=${className}Service.findById(id);
		return RestApiResponse.ok(${className});
	}
	
	/**
	 * 分页查询${functionName}
	 *@param ${className}Vo ${functionName} 查询条件
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@PostMapping("/findPageByQuery")
	@Operation(summary="分页查询${functionName}")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody ${ClassName}Vo ${className}Vo) {
		PageInfo<${ClassName}>  ${className}=${className}Service.findPageByQuery(${className}Vo);
		return RestApiResponse.ok(${className});
	}

	@PostMapping("/excel")
    @Operation(summary="导出${functionName}")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "${functionName}", sonMod = "${functionName}", key = "${moduleName}_${businessName}_Table", isNotice = true,  fileName = "${functionName}")
    public RestApiResponse<?> excel(@RequestBody ${ClassName}Vo ${className}Vo) {
        List<${ClassName}> ${className}List=${className}Service.findByQuery(${className}Vo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(${className}List);
    }

    @PostMapping("/import")
    @Operation(summary="导入${functionName}")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        ${className}Service.import${ClassName}Async(file,cover);
        return RestApiResponse.ok();
    }
}
