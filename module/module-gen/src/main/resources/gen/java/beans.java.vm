package ${packageName}.${moduleName}.bean;

#foreach ($import in $importList)
import ${import};
#end
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import ${packageName}.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * ${functionName}对象 ${tableName}
 
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Table(name = "${tableName}")
@Schema(description = "${functionName}")
public class ${ClassName} extends BaseEntity{
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
#if($column.javaType == "Long")
#set($jdbcType="BIGINT")
#elseif($column.javaType == "Integer")
#set($jdbcType="INTEGER")
#elseif($column.javaType == "Date")
#set($jdbcType="TIMESTAMP")
#elseif($column.javaType == "String")
#if($column.columnType == "CLOB")
#set($jdbcType="CLOB")
#elseif($column.columnType != "CLOB")
#set($jdbcType="VARCHAR")
#end
#elseif($column.javaType == "BigDecimal")
#set($jdbcType="DECIMAL")
#end
	@Column(name = "${column.columnName}")
    @ColumnType(jdbcType = JdbcType.${jdbcType})
    @Schema(description ="${column.columnComment}")
#if($column.columnType == "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JSONField(format="yyyy-MM-dd")
#end
#if($column.columnType == "datetime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
#end
#if($column.columnType == "timestamp")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
#end
	private $column.javaType $column.javaField;
#end
#end
#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
#if($column.javaField.length() > 2 && $column.javaField.substring(1,2).matches("[A-Z]"))
#set($AttrName=$column.javaField)
#else
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#end
    /**
     * SET $column.columnComment
     * @param $column.javaField
     */
    public void set${AttrName}($column.javaType $column.javaField){
#if($column.javaType=="String")
		this.$column.javaField = $column.javaField == null ? null :$column.javaField.trim();
#else
		this.$column.javaField = $column.javaField;
#end
	}
    /**
     * GET $column.columnComment
     * @return $column.javaField
     */
    public $column.javaType get${AttrName}(){
        return $column.javaField;
    }
#end
#end
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
