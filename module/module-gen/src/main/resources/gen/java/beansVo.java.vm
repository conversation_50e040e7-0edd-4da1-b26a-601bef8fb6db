package ${packageName}.${moduleName}.bean.vo;

import ${packageName}.${moduleName}.bean.${ClassName};
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * ${functionName}Vo
 *<AUTHOR>
 *@date ${datetime}
 */
@Schema(description = "${ClassName}Vo")
public class ${ClassName}Vo extends ${ClassName}{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}