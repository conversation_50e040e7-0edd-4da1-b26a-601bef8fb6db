-- 菜单 SQL
SELECT @parentId := replace( uuid(),'-','');

insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(@parentId,'${functionName}', '${parentMenuId}', '1', '${businessName}', '${moduleName}/${businessName}/index', 1, 0, 'C', '0', '0', '${permissionPrefix}:list', '#', 'admin', sysdate(), 'admin', sysdate(), '${functionName}菜单');

-- 按钮父菜单ID


-- 按钮 SQL
insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(replace( uuid(),'-',''),'${functionName}查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'btn:${permissionPrefix}:query',        '#', 'admin', sysdate(), 'admin', sysdate(), '');

insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(replace( uuid(),'-',''),'${functionName}新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'btn:${permissionPrefix}:save',          '#', 'admin', sysdate(), 'admin', sysdate(), '');

insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(replace( uuid(),'-',''),'${functionName}修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'btn:${permissionPrefix}:update',         '#', 'admin', sysdate(), 'admin', sysdate(), '');

insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(replace( uuid(),'-',''),'${functionName}删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'btn:${permissionPrefix}:delete',       '#', 'admin', sysdate(), 'admin', sysdate(), '');

insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(replace( uuid(),'-',''),'${functionName}详情', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'btn:${permissionPrefix}:find',       '#', 'admin', sysdate(), 'admin', sysdate(), '');

insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(replace( uuid(),'-',''),'${functionName}导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'btn:${permissionPrefix}:excel',       '#', 'admin', sysdate(), 'admin', sysdate(), '');

insert into sys_menu (id,menu_name, parent_id, order_val, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_user, create_time, update_user, update_time, remark)
values(replace( uuid(),'-',''),'${functionName}导入', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'btn:${permissionPrefix}:import',       '#', 'admin', sysdate(), 'admin', sysdate(), '');


INSERT INTO sys_table_config(id, table_flag, mod_type, son_mod, table_name, export_json, show_json, yn)
VALUES (replace( uuid(),'-',''), '${moduleName}_${businessName}_Table', '${functionName}', '${functionName}', '${functionName}', '[#foreach ($column in $columns){"key":"${column.javaField}","name":"${column.columnComment}"}#if ($velocityCount == $columns.size())#else,#end#end]', '[#foreach ($column in $columns){"fieldName":"${column.javaField}","fieldCnName":"${column.columnComment}","orderVal":null,"whetherShow":true,"whetherSort":true,"width":null,"whetherForceShow":false,"fieldType":0}#if ($velocityCount == $columns.size())#else,#end#end]', 1);
