package com.jh.sys.controller;

import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysTableSet;
import com.jh.sys.bean.vo.SysTableSetVo;
import com.jh.sys.service.SysTableSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户格设置Controller
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
@RestController
@RequestMapping("/sys/tableset")
@Tag(name="用户格设置")
public class SysTableSetController extends BaseController {
    @Autowired
    private SysTableSetService sysTableSetService;
	/**
	 * 新增用户格设置
	 *@param sysTableSet 用户格设置数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	  @Operation(summary="新增用户格设置")
	@SystemLogAnnotation(type = "用户格设置",value = "新增用户格设置")
	public RestApiResponse<?> saveSysTableSet(@RequestBody SysTableSetVo sysTableSet) {
		String id = sysTableSetService.saveListTableset(sysTableSet);
		return RestApiResponse.ok(id);
	}
	/**
	 * 新增列显示字段
	 *
	 * @param sysTableSetVo 导出字段数据 json
	 * @return RestApiResponse<?>
	 * <AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/saveListTableset")
	  @Operation(summary="新增列显示字段")
	@SystemLogAnnotation(type = "列显示字段", value = "新增列显示字段")
	//@PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
	public RestApiResponse<?> saveListTableset(@RequestBody SysTableSetVo sysTableSetVo) {
		String id = sysTableSetService.saveListTableset(sysTableSetVo);
		return RestApiResponse.ok(id);
	}


	/**
	 * 查询列显示字段详情
	 *
	 * @return RestApiResponse<?>
	 * <AUTHOR>
	 */
	@GetMapping("/findByCurrentUser")
	  @Operation(summary="查询列显示字段详情")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findByCurrentUser(@RequestParam("key") String key) {
		SysTableSet sysTableSet = sysTableSetService.findByKeyAndUserId(key, getCurrentUserId());
		return RestApiResponse.ok(sysTableSet);
	}

	/**
	 * 查询列导出字段详情
	 *
	 * @return RestApiResponse<?>
	 * <AUTHOR>
	 */
	@GetMapping("/findByCurrentUserForExport")
	  @Operation(summary="查询列导出字段详情")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findByCurrentUserForExport(@RequestParam("key") String key) {
		SysTableSet sysTableSet = sysTableSetService.findByCurrentUserForExport(key, getCurrentUserId());
		return RestApiResponse.ok(sysTableSet);
	}

	/**
	 * 查询列显示字段详情
	 *
	 * @return RestApiResponse<?>
	 * <AUTHOR>
	 */
	@GetMapping("/findListHabitByCurrentUser")
	  @Operation(summary="查询列显示字段详情")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findListHabitByCurrentUser(@RequestParam("key") String key) {
		SysTableSet sysTableSet = sysTableSetService.findByKeyAndUserId(key, getCurrentUserId());
		return RestApiResponse.ok(sysTableSet);
	}

}
