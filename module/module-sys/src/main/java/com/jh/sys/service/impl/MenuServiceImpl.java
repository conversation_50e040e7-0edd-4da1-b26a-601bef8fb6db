package com.jh.sys.service.impl;

import com.jh.common.constant.Constant;
import com.jh.common.service.BaseServiceImpl;
import com.jh.sys.bean.SysMenu;
import com.jh.sys.bean.vo.MetaVo;
import com.jh.sys.bean.vo.RouterVo;
import com.jh.sys.bean.vo.SysMenuVo;
import com.jh.sys.bean.vo.TreeSelect;

import com.jh.common.exception.ServiceException;
import com.jh.sys.dao.SysMenuMapper;
import com.jh.common.util.ExampleUtil;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.util.txt.StringUtils;
import com.jh.sys.service.MenuService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单service实现类
 *
 * <AUTHOR>
 * @date 2018/03/01
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MenuServiceImpl extends BaseServiceImpl<SysMenuMapper, SysMenu> implements MenuService {

    public static final Logger logger = LoggerFactory.getLogger(MenuServiceImpl.class);

    @Autowired
    SysMenuMapper sysMenuMapper;

    /**
     * 获取菜单列表
     *
     * @param menu   查询条件
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public List<SysMenuVo> selectMenuList(SysMenuVo menu, String userId) {
        List<SysMenu> menuList = menu.isAdmin()
                ? listMenuByQuery(menu)
                : sysMenuMapper.selectAuthorityMenuByUserId(userId);
        if (CollectionUtils.isEmpty(menuList)) {
            return Collections.emptyList();
        }

        return menuList.stream()
                .map(aMenu -> {
                    SysMenuVo vo = new SysMenuVo();
                    BeanUtils.copyProperties(aMenu, vo);
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 获取菜单列表
     *
     * @param menu 查询条件
     * @return 结果
     */
    private List<SysMenu> listMenuByQuery(SysMenuVo menu) {
        Example example = new Example(SysMenu.class);
        Criteria criteria = example.createCriteria();
        ExampleUtil.and(criteria, "menuName", menu.getMenuName(), ExampleUtil.LIKE);
        ExampleUtil.and(criteria, "visible", menu.getVisible(), ExampleUtil.EQUAL);
        ExampleUtil.and(criteria, "status", menu.getVisible(), ExampleUtil.EQUAL);
        example.orderBy("orderVal").asc();
        return sysMenuMapper.selectByExample(example);
    }

    /**
     * 删除菜单
     *
     * @param id
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void deleteMenuById(String id) {
        SysMenu dbSysMenu = sysMenuMapper.selectByPrimaryKey(id);
        if (dbSysMenu == null) {
            throw new ServiceException("当前菜单不存在，操作失败");
        }

        Example example = new Example(SysMenu.class);
        example.createCriteria().andEqualTo("parentId", id);
        int count = this.mapper.selectCountByExample(example);
        if (count > 0) {
            throw new ServiceException("有子菜单，操作失败");
        }
        this.mapper.deleteByPrimaryKey(id);

    }

    @Override
    public List<SysMenu> selectList(SysMenu sysMenu) {
        return sysMenuMapper.select(sysMenu);
    }

    @Override
    public SysMenu selectById(Object id) {
        return sysMenuMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SysMenu> selectListAll() {
        return this.sysMenuMapper.selectAll();
    }

    @Override
    // @CacheClear(keys={"permission:menu","permission"})
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void insertSelective(SysMenu entity) {
        if (!StringUtils.isEmpty(entity.getId())) {
            if ("0".equals(entity.getId())) {
                entity.setId(null);
            }
        }

        entity.setId(UUIDUtils.getUUID());
        // 校验CODE重复
        if (!checkCode(entity.getPerms(), entity.getId()) && !"M".equals(entity.getMenuType())) {
            throw new ServiceException("菜单权限编码已经存在");
        }
        sysMenuMapper.insertSelective(entity);
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int updateById(SysMenuVo vo) {
        SysMenu dbMenu = sysMenuMapper.selectByPrimaryKey(vo.getId());
        if (dbMenu == null) {
            throw new ServiceException("当前菜单不存在");
        }
        // 校验CODE重复
        if (!checkCode(vo.getPerms(), vo.getId()) && !"M".equals(vo.getMenuType())) {
            throw new ServiceException("菜单权限编码已经存在");
        }
        return sysMenuMapper.updateByPrimaryKeySelective(vo);
    }

    boolean checkCode(String code, String id) {
        Example example = new Example(SysMenu.class);
        example.createCriteria().andEqualTo("perms", code);
        List<SysMenu> list = sysMenuMapper.selectByExample(example);
        if (list.isEmpty()) {
            return true;
        }
        // 编辑
        if (id != null) {
            for (SysMenu bean : list) {
                if (!bean.getId().equals(id)) {
                    return false;
                }
            }
        } else {
            return false;
        }

        return true;
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int updateSelectiveById(SysMenu entity) {
        return sysMenuMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 获取用户可以访问的菜单
     *
     * @param userId
     * @return
     */
    @Override
    public List<SysMenu> getUserAuthorityMenuByUserId(String userId) {
        return sysMenuMapper.selectAuthorityMenuByUserId(userId);
    }

    @Override
    public List<SysMenu> getUserAuthorityMenuByRoleUserId(String roleCode) {
        return sysMenuMapper.selectAuthorityMenuByRoleUserId(roleCode);
    }

    @Override
    public List<SysMenu> selectByExample(Object example) {
        return sysMenuMapper.selectByExample(example);
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void addSysMenu(SysMenuVo vo) {

        boolean isAdmin = vo.isAdmin();

        logger.info("<addSysMenu><sysMenu>" + vo);
        String menuId = vo.getId();
        if (!StringUtils.isEmpty(vo.getPerms())) {
            Example example = new Example(SysMenu.class);
            example.createCriteria().andEqualTo("perms", vo.getPerms());
            int count = this.mapper.selectCountByExample(example);
            if (count > 0) {
                throw new ServiceException("权限编码重复");
            }
        }

        String parentId = vo.getParentId();
        if (parentId == null || Constant.ROOT.equals(parentId)) {
            parentId = Constant.ROOT;
            if (!isAdmin) {
                throw new ServiceException("无新增权限");
            }
            vo.setParentId(parentId);
        }

        if (StringUtils.isEmpty(menuId) || Constant.ROOT.equals(menuId)) {
            vo.setId(UUIDUtils.getUUID());
            this.insertSelective(vo);
        } else {
            this.sysMenuMapper.updateByPrimaryKeySelective(vo);
        }
    }

    /**
     * 根据角色id查询菜单
     */
    @Override
    public List<SysMenu> findRoleId(String roleId) {

        return sysMenuMapper.findRoleId(roleId);
    }

    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenuVo> menus) {
        List<SysMenuVo> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    private List<SysMenuVo> buildMenuTree(List<SysMenuVo> menus) {
        List<SysMenuVo> returnList = new ArrayList<SysMenuVo>();
        List<String> tempList = new ArrayList<String>();
        for (SysMenu dept : menus) {
            tempList.add(dept.getId());
        }
        for (Iterator<SysMenuVo> iterator = menus.iterator(); iterator.hasNext(); ) {
            SysMenuVo menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list   总的菜单列表
     * @param parent 上级菜单
     */
    private void recursionFn(List<SysMenuVo> list, SysMenuVo parent) {
        // 获取子菜单
        List<SysMenuVo> childList = list.stream().filter(p -> Objects.equals(p.getParentId(), parent.getId())).collect(Collectors.toList());
        parent.setChildren(childList);

        if (CollectionUtils.isEmpty(childList)) {
            return;
        }
        for (SysMenuVo child : childList) {
            recursionFn(list, child);
        }
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenuVo> menus) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenuVo menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden(Constant.YES.equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache() + "")));
            List<SysMenuVo> cMenus = menu.getChildren();
            if (!CollectionUtils.isEmpty(cMenus) && Constant.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMeunFrame(menu)) {
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache() + "")));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMeunFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    @Override
    public List<SysMenuVo> getChildPerms(List<SysMenuVo> list, String parentId) {
        List<SysMenuVo> returnList = new ArrayList<>();
        for (SysMenuVo vo : list) {
            if (parentId.equals(vo.getParentId())) {
                recursionFn(list, vo);
                returnList.add(vo);
            }
        }
        return returnList;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu) {
        String routerPath = menu.getPath();
        // 非外链并且是一级目录（类型为目录）
        if ("0".equals(menu.getParentId()) && Constant.TYPE_DIR.equals(menu.getMenuType()) && Constant.NO_FRAME.equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMeunFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenu menu) {
        String component = Constant.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMeunFrame(menu)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            component = Constant.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMeunFrame(SysMenu menu) {
        return "0".equals(menu.getParentId()) && Constant.TYPE_MENU.equals(menu.getMenuType()) && menu.getIsFrame().equals(Constant.NO_FRAME);
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(SysMenu menu) {
        return !"0".equals(menu.getParentId()) && Constant.TYPE_DIR.equals(menu.getMenuType());
    }
}
