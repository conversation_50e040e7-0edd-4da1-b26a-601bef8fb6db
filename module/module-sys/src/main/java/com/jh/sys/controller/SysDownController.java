package com.jh.sys.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysDown;
import com.jh.sys.bean.vo.SysDownVo;
import com.jh.sys.service.SysDownService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 我的导出Controller
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
@RestController
@RequestMapping("/sys/down")
@Tag(name="我的导出")
public class SysDownController extends BaseController {
    @Autowired
    private SysDownService sysDownService;

	private static final String PER_PREFIX = "btn:sys:down:";

	
	/**
	 * 批量删除我的导出(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@Operation(summary = "批量删除我的导出")
	@SystemLogAnnotation(type = "我的导出",value = "批量删除我的导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteSysDown(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		sysDownService.deleteSysDown(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 * 查询我的导出详情
	 *@param id
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@GetMapping("/findById")
	@Operation(summary = "查询我的导出详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		SysDown sysDown=sysDownService.findById(id);
		return RestApiResponse.ok(sysDown);
	}
	
	/**
	 * 分页查询我的导出
	 *@param sysDownVo 我的导出 查询条件
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@PostMapping("/findPageByQuery")
	@Operation(summary = "分页查询我的导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody SysDownVo sysDownVo) {
		PageInfo<SysDown> sysDown=sysDownService.findPageByQuery(sysDownVo);
		return RestApiResponse.ok(sysDown);
	}
	
}
