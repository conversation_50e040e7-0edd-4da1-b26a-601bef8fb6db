package com.jh.sys.bean.vo;


import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 行政区划中组织机构信息Vo
 *
 * <AUTHOR>
 * @date 2020-07-07 19:11:41
 */
@Schema(description = "行政区划中组织机构信息Vo")
public class SysDictVo implements Serializable {

    /**
     * @Fields serialVersionUID :
     */
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户类型")
    private String userType;

    @Schema(description = "是否管理员")
    private boolean whetherAdmin;

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public boolean getWhetherAdmin() {
        return whetherAdmin;
    }

    public void setWhetherAdmin(boolean whetherAdmin) {
        this.whetherAdmin = whetherAdmin;
    }

}
