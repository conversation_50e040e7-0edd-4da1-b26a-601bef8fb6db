package com.jh.sys.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.bean.SysLogRecord;
import com.jh.common.service.BaseServiceImpl;
import com.jh.sys.bean.vo.SysLogRecordExportVo;
import com.jh.sys.bean.vo.SysLogRecordVo;
import com.jh.common.exception.ServiceException;
import com.jh.sys.dao.SysLogMapper;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sys.service.SysLogService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

/**
 * 系统日志
 *
 * <AUTHOR>
 * @date 2019-11-04 14:33:24
 */
@Service
public class SysLogServiceImpl extends BaseServiceImpl<SysLogMapper, SysLogRecord> implements SysLogService {
    public static final Logger logger = LoggerFactory.getLogger(SysLogServiceImpl.class);

    @Autowired
    private SysLogMapper sysLogMapper;

    /**
     * 保存日志
     *
     * @param sysLogRecord 日志对像
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false)
    public String saveOrUpdateLogRecord(SysLogRecord sysLogRecord) {
        if (sysLogRecord == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(sysLogRecord.getId())) {
            // 新增
            sysLogRecord.setId(UUIDUtils.getUUID());
            sysLogMapper.insertSelective(sysLogRecord);
        }
        return sysLogRecord.getId();
    }

    /**
     * 查询日志记录
     *
     * @param id 日志ID
     * @return SysLogRecord
     */
    @Override
    public SysLogRecord findById(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new ServiceException("数据异常");
        }
        return sysLogMapper.selectByPrimaryKey(id);
    }

    /**
     * 分页查询
     *
     * @param logRecordvo 日志Vo
     * @return 分页数据
     */
    @Override
    public PageInfo<SysLogRecord> findPageByQuery(SysLogRecordVo logRecordvo) {
        PageHelper.startPage(logRecordvo.getPageNum(), logRecordvo.getPageSize());
        Example example = new Example(SysLogRecord.class);
        Criteria criteria = example.createCriteria();

        String[] operTime = logRecordvo.getOperTime();
        String sortField = logRecordvo.getSortField();

        if (!StringUtils.isEmpty(logRecordvo.getModule())) {
            criteria.andLike("module", "%" + logRecordvo.getModule() + "%");
        }
        if (!StringUtils.isEmpty(logRecordvo.getUserName())) {
            criteria.andLike("userName", "%" + logRecordvo.getUserName() + "%");
        }
        if (logRecordvo.getFlag() != null) {
            criteria.andEqualTo("flag", logRecordvo.getFlag());
        }

        if (operTime != null && operTime.length == 2) {
            criteria.andBetween("createTime", operTime[0], operTime[1]);
        }

        if (!StringUtils.isEmpty(sortField)) {
            if ("desc".equals(logRecordvo.getSortType())) {
                example.orderBy(sortField).desc();
            } else {
                example.orderBy(sortField).asc();
            }
        } else {
            example.orderBy("createTime").desc();
        }

        List<SysLogRecord> logRecordList = sysLogMapper.selectByExample(example);

        return new PageInfo<>(logRecordList);
    }

    /**
     * 导出日志信息
     *
     * @param sysLogRecordVo 日志Vo
     * @return 导出日志信息
     */
    @Override
    public List<SysLogRecordExportVo> export(SysLogRecordVo sysLogRecordVo) {
        return sysLogMapper.selectExportData(sysLogRecordVo);
    }
}
