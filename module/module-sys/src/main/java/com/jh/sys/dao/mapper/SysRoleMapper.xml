<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysRoleMapper">
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="YN" property="yn" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 系统角色表 -->
    <resultMap id="BaseResultMap" type="com.jh.common.bean.SysRole" extends="CommonBaseResultMap">
        <!-- WARNING - @mbg.generated -->
        <!-- 状态（启用,停用） -->
        <result column="STATUS_TXT" property="statusTxt" jdbcType="VARCHAR"/>
        <!-- 状态（1启用,0停用） -->
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <!-- 排序值 -->
        <result column="ORDER_VAL" property="orderVal" jdbcType="INTEGER"/>
        <!-- 备注信息 -->
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <!-- 角色名称 -->
        <result column="ROLE_NAME" property="roleName" jdbcType="VARCHAR"/>
        <!-- 角色编码 -->
        <result column="ROLE_CODE" property="roleCode" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="RoleAndRoleTypesVO" type="com.jh.sys.bean.vo.RoleAndRoleTypesVO" extends="BaseResultMap">
    </resultMap>
    <select id="listRoleAndRoleTypeVo" resultMap="RoleAndRoleTypesVO">
        SELECT r.* FROM sys_Role r
        <where>
            <if test="roleName != null and roleName !=''">
                <bind name="roleNameName" value="'%' + roleName + '%'"/>
                and r.Role_Name like #{roleNameName}
            </if>
            <if test="roleCode != null and roleCode !=''">
                and r.Role_Code = #{roleCode}
            </if>
        </where>
    </select>
    <select id="listRoleVOByUserId" resultMap="RoleAndRoleTypesVO">
        SELECT r.*
        FROM sys_Role r
                 right join
             sys_user_role ur on ur.role_id = r.id
        where ur.User_Id =
              #{userId}
        Order By r.Order_Val
    </select>
    <select id="listRoleAndRoleNameVo" resultMap="RoleAndRoleTypesVO">
        SELECT r.*
        FROM sys_Role r
        where yn=1
        <if test="sysRoleVo.roleName != null and sysRoleVo.roleName !=''">
            <bind name="searchRoleName" value="'%' + sysRoleVo.roleName + '%'"/>
            and r.Role_Name like #{searchRoleName}
        </if>
        order by
        <if test="sysRoleVo.sortField != null and sysRoleVo.sortField !='' and sysRoleVo.sortType != null and sysRoleVo.sortType !=''">
            ${sysRoleVo.sortField} ${sysRoleVo.sortType},
        </if>
        ORDER_VAL asc, UPDATE_TIME desc
    </select>
    <select id="listRoleByLoginUser" resultType="com.jh.common.bean.SysRole">
        SELECT r.*
        FROM sys_Role r
        where yn = 1
        order by r.order_val desc
    </select>
    <select id="getByUserId" resultMap="BaseResultMap">
        SELECT r.*
        FROM sys_Role r
                 LEFT JOIN sys_user_role ur ON
            r.ID = ur.Role_ID
        where r.status = 1
          and ur.User_Id =
              #{userId}
    </select>
    <update id="deleteRoleByIds">
        update sys_Role set yn=0 where ID in
        <foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </update>
    <!-- 导出 -->
    <select id="export" resultType="com.jh.sys.bean.vo.SysRoleExportVo">
        SELECT
        r.role_name,r.remark from sys_role r where r.yn=1
        <if test="sysRoleVo.roleName != null and sysRoleVo.roleName !=''">
            <bind name="searchRoleName" value="'%' + sysRoleVo.roleName + '%'"/>
            and r.Role_Name like #{searchRoleName}
        </if>
        order by r.order_val desc
    </select>

    <!-- 获取角色名或角色code的角色数量 -->
    <select id="countCheckRoleNameOrRoleCode" resultType="java.lang.Integer">
        select count(1) from sys_role where YN=1 and (ROLE_NAME=#{roleName} or ROLE_CODE=#{roleCode})
        <if test="id != null and id != ''">
            and ID!=#{id}
        </if>
    </select>

    <!-- 根据用户Id获取角色 -->
    <select id="listByUserId" resultType="com.jh.common.bean.SysRole">
        SELECT a.*
        FROM sys_role a
        WHERE a.ID in (SELECT ROLE_ID
                       from sys_user_role b
                                join sys_user c on c.ID = b.USER_ID
                       WHERE c.ID = #{userId})
    </select>
</mapper>