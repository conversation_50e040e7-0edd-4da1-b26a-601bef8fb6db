<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysTableConfigMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 表格基础配置 -->
  <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysTableConfig" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 表标识 -->
    <result column="table_flag" property="tableFlag" jdbcType="VARCHAR" />
    <!-- 模块名 -->
    <result column="mod_type" property="modType" jdbcType="VARCHAR" />
    <!-- 子模块名 -->
    <result column="son_mod" property="sonMod" jdbcType="VARCHAR" />
    <!-- 表格名称 -->
    <result column="table_name" property="tableName" jdbcType="VARCHAR" />
    <!-- 导出json数组配置 -->
    <result column="export_json" property="exportJson" jdbcType="VARCHAR" />
    <!-- 显示json数组配置 -->
    <result column="show_json" property="showJson" jdbcType="VARCHAR" />
  </resultMap>
</mapper>