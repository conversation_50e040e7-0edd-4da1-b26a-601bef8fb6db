package com.jh.sys.service.impl;

import com.jh.common.bean.SysFileInfo;
import com.jh.sys.service.FileOperationService;
import com.jh.sys.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class FileOperationServiceImpl implements FileOperationService {
    @Value("${file.useFileType}")
    private String fileSource;

    @Autowired
    private FileServiceFactory fileServiceFactory;
    @Override
    public SysFileInfo upload(MultipartFile file) throws Exception {
        FileService fileService = fileServiceFactory.getFileService(fileSource);
        SysFileInfo sysFileInfo = new SysFileInfo();
        return fileService.upload(file, sysFileInfo);
    }
}
