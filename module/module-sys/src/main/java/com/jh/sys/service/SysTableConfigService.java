package com.jh.sys.service;

import com.alibaba.fastjson2.JSONArray;
import com.github.pagehelper.PageInfo;
import com.jh.sys.bean.SysTableConfig;
import com.jh.sys.bean.vo.DataFiled;
import com.jh.sys.bean.vo.SysTableConfigVo;

import java.util.List;

/**
 * 表格基础配置Service接口
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface SysTableConfigService {
    /**
     * 保存或更新表格基础配置
     *
     * @param sysTableConfig 表格基础配置对象
     * @return String 表格基础配置ID
     * <AUTHOR>
     */
    String saveOrUpdateSysTableConfig(SysTableConfig sysTableConfig);

    /**
     * 删除表格基础配置
     *
     * @param ids void 表格基础配置ID
     * <AUTHOR>
     */
    void deleteSysTableConfig(List<String> ids);

    /**
     * 查询表格基础配置详情
     *
     * @param id
     * @return SysTableConfig
     * <AUTHOR>
     */
    SysTableConfig findById(String id);

    /**
     * 分页查询表格基础配置
     *
     * @param sysTableConfigVo
     * @return PageInfo<SysTableConfig>
     * <AUTHOR>
     */
    PageInfo<SysTableConfig> findPageByQuery(SysTableConfigVo sysTableConfigVo);

    JSONArray findExcelJsonByFlag(String tableFlag);

    JSONArray findListJsonByFlag(String tableFlag);
}
