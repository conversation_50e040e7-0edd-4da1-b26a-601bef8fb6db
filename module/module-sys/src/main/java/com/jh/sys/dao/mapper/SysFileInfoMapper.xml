<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jh.sys.dao.SysFileInfoMapper">
<resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 文件表 -->
  <resultMap id="BaseResultMap" type="com.jh.common.bean.SysFileInfo" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 文件名 -->
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <!-- 是否是图片1是0否 -->
    <result column="ISIMG" property="isimg" jdbcType="INTEGER" />
    <!-- 文件类型 -->
    <result column="CONTENT_TYPE" property="contentType" jdbcType="VARCHAR" />
    <!-- 文件大小 -->
    <result column="SIZE" property="size" jdbcType="INTEGER" />
    <!-- 物理路径 -->
    <result column="PATH" property="path" jdbcType="VARCHAR" />
    <!-- 文件网络url -->
    <result column="URL" property="url" jdbcType="VARCHAR" />
    <!-- 文件存储地方 -->
    <result column="SOURCE" property="source" jdbcType="VARCHAR" />
  </resultMap>
</mapper>