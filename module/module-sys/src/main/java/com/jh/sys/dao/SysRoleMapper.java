package com.jh.sys.dao;

import com.jh.common.bean.SysRole;
import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sys.bean.vo.RoleAndRoleTypesVO;
import com.jh.sys.bean.vo.SysRoleExportVo;
import com.jh.sys.bean.vo.SysRoleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
public interface SysRoleMapper extends BaseInfoMapper<SysRole> {

    /**
     * 通过用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 结果
     */
    List<SysRole> getByUserId(@Param("userId") String userId);

    /**
     * 逻辑删除
     *
     * @param roleIds 角色IDs
     */
    void deleteRoleByIds(@Param("roleIds") String[] roleIds);

    /**
     * 获取角色
     *
     * @param roleName 角色名
     * @param roleCode 角色Code
     * @return 结果
     */
    List<RoleAndRoleTypesVO> listRoleAndRoleTypeVo(@Param("roleName") String roleName, @Param("roleCode") String roleCode);

    /**
     * 获取角色
     *
     * @param sysRoleVo 角色Vo
     * @return 结果
     */
    List<RoleAndRoleTypesVO> listRoleAndRoleNameVo(@Param("sysRoleVo") SysRoleVo sysRoleVo);

    /**
     * 查询当前用户添加的角色列表
     *
     * @param sysRoleVo 角色Vo
     * @return 结果
     */
    List<SysRole> listRoleByLoginUser(@Param("sysRoleVo") SysRoleVo sysRoleVo);

    /**
     * 获取用户拥有的角色信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    List<RoleAndRoleTypesVO> listRoleVOByUserId(@Param("userId") String userId);

    /**
     * 导出
     *
     * @param sysRoleVo 角色Vo
     * @return 结果
     */
    List<SysRoleExportVo> export(@Param("sysRoleVo") SysRoleVo sysRoleVo);

    /**
     * 获取角色名或角色code是否重复
     *
     * @param roleCode 角色code
     * @param roleName 角色名
     * @param id       角色Id
     * @return 结果
     */
    int countCheckRoleNameOrRoleCode(@Param("roleCode") String roleCode, @Param("roleName") String roleName, @Param("id") String id);

    /**
     * 根据用户Id获取角色信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    List<SysRole> listByUserId(@Param("userId") String userId);
}