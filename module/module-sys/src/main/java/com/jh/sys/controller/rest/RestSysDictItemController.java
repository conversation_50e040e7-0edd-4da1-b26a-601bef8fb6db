package com.jh.sys.controller.rest;

import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysDictItem;
import com.jh.sys.service.SysDictItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 可匿名访问的字典接口
 */
@RestController
@RequestMapping("/sys/dict/rest")
@Tag(name = "字典RESTAPI")
public class RestSysDictItemController extends BaseController {

    @Autowired
    private SysDictItemService basicDictItemService;

    /**
     * 根据字典CODE得到下级结点列表一级
     *
     * @param typeCode 字典编码
     * @return 子节点数据
     */
    @GetMapping("/findDictItemByDictType")
      @Operation(summary="根据字典类型获取字典条目")
    public List<SysDictItem> findDictItemByDictType(@RequestParam(value = "typeCode", required = true) String typeCode) {
        return basicDictItemService.findDictItemByDictCode(typeCode);
    }

    /**
     * 根据字典编码获取字典
     *
     * @param code 字典编码
     * @return 字典信息
     */
    @GetMapping("/findDictItem")
     @Operation(summary="根据字典编码获取字典")
    public SysDictItem findDictItem(@RequestParam(value = "code", required = true) String code) {
        return basicDictItemService.findDictByCode(code);
    }
}
