package com.jh.sys.dao;

import com.jh.common.bean.SysLogRecord;
import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sys.bean.vo.SysLogRecordExportVo;
import com.jh.sys.bean.vo.SysLogRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统日志
 *
 * <AUTHOR>
 * @date 2019-11-04 14:33:24
 */
public interface SysLogMapper extends BaseInfoMapper<SysLogRecord> {

    /**
     * @param sysLogRecordVo
     * @return List<SysLogRecordExportVo>
     * @Description:查询导出日志数据
     * <AUTHOR>
     */
    List<SysLogRecordExportVo> selectExportData(@Param("sysLogRecordVo") SysLogRecordVo sysLogRecordVo);

}