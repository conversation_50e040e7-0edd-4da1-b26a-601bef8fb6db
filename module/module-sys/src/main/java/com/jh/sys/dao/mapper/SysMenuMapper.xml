<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysMenuMapper">
    <resultMap id="CommonBaseResultMap"
               type="com.jh.common.bean.BaseEntity">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="YN" property="yn" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime"
                jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser"
                jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser"
                jdbcType="VARCHAR"/>
        <result column="CREATE_USER_NICKNAME"
                property="createUserNickname" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NICKNAME"
                property="updateUserNickname" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 系统菜单表 -->
    <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysMenu" extends="CommonBaseResultMap">
        <!--
          WARNING - @mbg.generated
        -->
        <!-- 资源名称 -->
        <result column="MENU_NAME" property="menuName" jdbcType="VARCHAR"/>
        <!-- 父ID -->
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
        <!-- 排序值 -->
        <result column="ORDER_VAL" property="orderVal" jdbcType="INTEGER"/>
        <!-- 路由地址 -->
        <result column="PATH" property="path" jdbcType="VARCHAR"/>
        <!-- 组件路径 -->
        <result column="COMPONENT" property="component" jdbcType="VARCHAR"/>
        <!-- 是否为外链（0是 1否） -->
        <result column="IS_FRAME" property="isFrame" jdbcType="INTEGER"/>
        <!-- 是否缓存（0缓存 1不缓存） -->
        <result column="IS_CACHE" property="isCache" jdbcType="INTEGER"/>
        <!-- 菜单类型（M目录 C菜单 F按钮） -->
        <result column="MENU_TYPE" property="menuType" jdbcType="VARCHAR"/>
        <!-- 菜单状态（0显示 1隐藏） -->
        <result column="VISIBLE" property="visible" jdbcType="INTEGER"/>
        <!-- 菜单状态（0正常 1停用） -->
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <!-- 权限标识 -->
        <result column="PERMS" property="perms" jdbcType="VARCHAR"/>
        <!-- 图标 -->
        <result column="ICON" property="icon" jdbcType="VARCHAR"/>
        <!-- 备注信息 -->
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectAuthorityMenuByUserId" resultMap="BaseResultMap">
        SELECT d.*
        FROM sys_role_permission c
                 join sys_menu d on d.ID = c.RESOURCE_ID
        WHERE ROLE_CODE in (SELECT b.ROLE_CODE
                            FROM sys_user_role a
                                     join sys_role b on b.ID = a.ROLE_ID
                            WHERE b.`STATUS` = 1
                              AND a.USER_ID = #{userId})
        ORDER BY d.ORDER_VAL ASC
    </select>

    <!-- 根据角色id查询菜单 -->
    <select id="findRoleId" resultMap="BaseResultMap">
        SELECT mu.*
        FROM sys_Menu mu,
             sys_role_permission rolePer
        WHERE mu.ID = rolePer.Resource_Id
          and rolePer.Role_Code = (
            SELECT Role_Code
            FROM sys_Role
            WHERE ID = #{roleId}
        )
    </select>

    <select id="selectAuthorityMenuByRoleUserId"
            resultMap="BaseResultMap">
        SELECT DISTINCT sm.*
        FROM sys_Role sr,
             sys_role_permission srp,
             sys_Menu sm
        WHERE srp.Role_Code = sr.Role_Code
          AND sr.Role_Code = #{roleCode}
          AND sm.ID = srp.Resource_Id
          AND srp.Resource_Type = 'menu'
        order by sm.Order_Val asc
    </select>

</mapper>