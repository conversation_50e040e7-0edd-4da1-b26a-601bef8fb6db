package com.jh.sys.service.impl;

import com.jh.common.bean.SysRole;
import com.jh.common.constant.Constant;
import com.jh.common.service.BaseServiceImpl;
import com.jh.sys.bean.SysRolePermission;
import com.jh.sys.bean.vo.PermissionVO;
import com.jh.common.exception.ServiceException;
import com.jh.sys.dao.SysRoleMapper;
import com.jh.sys.dao.SysRolePermissionMapper;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sys.service.RolePermissionService;
import com.jh.sys.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 角色service实现类
 *
 * <AUTHOR>
 * @date 2018/03/1
 */
@Service
@Transactional(readOnly = true)
public class RolePermissionServiceImpl extends BaseServiceImpl<SysRolePermissionMapper, SysRolePermission>
        implements RolePermissionService {

//	@Resource

//    private ExceptionManager exceptionManager;

    @Autowired
    private RoleService roleService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysRolePermissionMapper sysRolePermissionMapper;

    /**
     * 保存权限
     *
     * @param roleId      角色ID
     * @param permissions 权限
     */
    @Override
    @Transactional(readOnly = false)
    public void savePermissions(String roleId, String menuId, List<String> permissions, String type) {
        if (roleId == null) {
            return;
        }
        SysRole role = new SysRole();
        role.setId(roleId);

        SysRole roleObj = this.roleService.selectOne(role);
        if (roleObj == null) {
            throw new ServiceException("未找到角色信息!");
        }

        String roleCode = roleObj.getRoleCode();

        /*** delete permission **/
        this.mapper.deletePermissionByRoleCode(roleCode, type, menuId);

        if (permissions != null) {
            for (String permission : permissions) {
                if (!permission.equals("0")) {
                    if (PermissionVO.TYPE_MENU.equals(type)) {
                        SysRolePermission per = new SysRolePermission();
                        per.setResourceId(permission);
                        per.setResourceType(Constant.RESOURCE_TYPE_MENU);
                        per.setRoleCode(roleCode);
                        per.setId(UUIDUtils.getUUID());
                        sysRolePermissionMapper.insertSelective(per);
                    } else if (PermissionVO.TYPE_BTN.equals(type)) {
                        SysRolePermission per = new SysRolePermission();
                        per.setResourceId(permission);
                        per.setResourceType(Constant.RESOURCE_TYPE_BTN);
                        per.setRoleCode(roleCode);
                        per.setId(UUIDUtils.getUUID());
                        sysRolePermissionMapper.insertSelective(per);
                    }
                }
            }
        }
    }

    /**
     * 查询角色权限
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public List<SysRolePermission> listPermissions(String roleId) {
        if (roleId == null) {
            return null;
        }

        SysRole roleObj = this.sysRoleMapper.selectByPrimaryKey(roleId);
        if (roleObj == null) {
            throw new ServiceException("未找到角色信息!");
        }

        Example example = new Example(SysRolePermission.class);
        example.createCriteria().andEqualTo("roleCode", roleObj.getRoleCode());
        return this.mapper.selectByExample(example);
    }
}
