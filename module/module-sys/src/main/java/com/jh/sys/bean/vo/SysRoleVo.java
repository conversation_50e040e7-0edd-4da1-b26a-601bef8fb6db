package com.jh.sys.bean.vo;

import com.jh.common.bean.SysRole;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class SysRoleVo extends SysRole {

    /**
     *
     */
    private static final long serialVersionUID = 7409911912621383197L;

    private List<String> menuIds;

    private String roleTypes;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    /**
     * 是否是级管理员，true是；false否
     */
    private boolean isAdmin;

    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序类型
     */
    private String sortType;

    public List<String> getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(List<String> menuIds) {
        this.menuIds = menuIds;
    }

    public boolean isAdmin() {
        return isAdmin;
    }

    public void setAdmin(boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        if (!StringUtils.isEmpty(sortField)) {

        }
        this.sortField = sortField;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getRoleTypes() {
        return roleTypes;
    }

    public void setRoleTypes(String roleTypes) {
        this.roleTypes = roleTypes;
    }

}
