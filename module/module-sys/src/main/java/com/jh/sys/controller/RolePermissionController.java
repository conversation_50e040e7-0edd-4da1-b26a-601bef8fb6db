package com.jh.sys.controller;

import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysRolePermission;
import com.jh.sys.bean.vo.PermissionVO;
import com.jh.sys.service.RolePermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/sys/userRole")
@Tag(name = "角色权限接口")
public class RolePermissionController extends BaseController {
    private static final String prefix = "btn:sys:role:";
    @Autowired
    private RolePermissionService rolePermissionService;

    @RepeatSubAnnotation
    @PostMapping(value = "/savePermissionByRoleId")
    @ResponseBody
      @Operation(summary="查询角色权限")
    @PreAuthorize("hasAuthority('" + prefix + "setPermission')")
    public RestApiResponse<?> savePermissionByRoleId(String roleId, String menuId,
                                                     @RequestBody List<String> permissions) {
        this.rolePermissionService.savePermissions(roleId, menuId, permissions, PermissionVO.TYPE_BTN);
        return RestApiResponse.ok();

    }

    @RepeatSubAnnotation
    @PostMapping(value = "/savePermissionMenuByRoleId")
    @ResponseBody
      @Operation(summary="查询角色菜单权限")
    @PreAuthorize("hasAuthority('" + prefix + "setMenu')")
    public RestApiResponse<?> savePermissionMenuByRoleId(String roleId, @RequestBody List<String> permissions) {
        this.rolePermissionService.savePermissions(roleId, null, permissions, PermissionVO.TYPE_MENU);
        return RestApiResponse.ok();

    }

    @GetMapping(value = "/listPermissionByRoleId")
    @ResponseBody
      @Operation(summary="查询角色权限")
    @PreAuthorize("hasAuthority('" + prefix + "permissionByRoleId')")
    public RestApiResponse<?> listPermissionByRoleId(String roleId) {
        List<SysRolePermission> dataList = this.rolePermissionService.listPermissions(roleId);
        return RestApiResponse.ok(dataList);
    }

}
