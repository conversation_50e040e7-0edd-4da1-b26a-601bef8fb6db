package com.jh.sys.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Description 描述
 * @Date 2023/9/13 15:51
 * <AUTHOR>
 **/

@Data
@Component
@ConfigurationProperties(prefix = SuperviseProperties.PREFIX)
public class SuperviseProperties {

    public final static String PREFIX = "expert.supervise";

    /**
     * 默认密码
     */
    private String password;

    /**
     * 默认角色
     */
    private String roleCode;

    private String userTypeCode;



    /**
     * 是否开启专家只能手机号登录
     */
    private Boolean smsLogin = false;


}
