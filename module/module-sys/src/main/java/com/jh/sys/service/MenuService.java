package com.jh.sys.service;

import com.jh.common.service.BaseService;
import com.jh.sys.bean.SysMenu;
import com.jh.sys.bean.vo.RouterVo;
import com.jh.sys.bean.vo.SysMenuVo;
import com.jh.sys.bean.vo.TreeSelect;

import java.util.List;

public interface MenuService extends BaseService<SysMenu> {



    int updateById(SysMenuVo vo);

    List<SysMenu> getUserAuthorityMenuByUserId(String userId);

    List<SysMenu> getUserAuthorityMenuByRoleUserId(String roleCode);

    void addSysMenu(SysMenuVo vo);

    /**
     * 删除菜单
     *
     * @param id
     */
    void deleteMenuById(String id);

    /**
     * 角色id查询权限
     *
     * @param roleId
     * @return
     */
    List<SysMenu> findRoleId(String roleId);

    /**
     * 根据用户查询菜单列表不带分页
     *
     * @param menu   查询条件
     * @param userId 用户ID
     * @return 结果
     */
    List<SysMenuVo> selectMenuList(SysMenuVo menu, String userId);

    /**
     * 菜单转树形
     *
     * @param menus
     * @return
     */
    List<TreeSelect> buildMenuTreeSelect(List<SysMenuVo> menus);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    List<RouterVo> buildMenus(List<SysMenuVo> menus);

    /**
     * 递归
     *
     * @param menus
     * @param p
     * @return
     */
    List<SysMenuVo> getChildPerms(List<SysMenuVo> menus, String p);

}
