package com.jh.sys.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.CommonConstant;
import com.jh.common.service.BaseServiceImpl;
import com.jh.sys.bean.VersionHash;
import com.jh.sys.bean.vo.VersionHashVo;
import com.jh.common.exception.ServiceException;
import com.jh.sys.dao.VersionHashMapper;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sys.service.VersionHashService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 *  【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
@Transactional(readOnly = true)
public class VersionHashServiceImpl extends BaseServiceImpl<VersionHashMapper, VersionHash> implements VersionHashService {
	
	private static final Logger logger = LoggerFactory.getLogger(VersionHashServiceImpl.class);
    @Autowired
    private VersionHashMapper versionHashMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新【新增版本信息】
	 *@param versionHash 【新增版本信息】对象
	 *@return String 【新增版本信息】ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateVersionHash(VersionHash versionHash) throws IOException, NoSuchAlgorithmException {
		if(versionHash==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(versionHash.getId())){
			//新增
			versionHash.setId(UUIDUtils.getUUID());
			//String hs=JarHashCalculator.calculateHash();
			//versionHash.setVersionHash(hs);
			versionHashMapper.insertSelective(versionHash);
		}else{
			//避免页面传入修改
			versionHash.setYn(null);
			versionHashMapper.updateByPrimaryKeySelective(versionHash);
		 }

		return versionHash.getId();
	}


	/**
	 * 查询【获取最新版本】详情
	 *
	 * @param
	 * @return VersionHash
	 * <AUTHOR>
	 */
	@Override
	public VersionHash findByVersionHash() {
		return versionHashMapper.findByVersionHash();
	}




	/**
	 * 分页查询【请填写功能名称】
	 *@param versionHashVo
	 *@return PageInfo<VersionHash>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<VersionHash> findPageByQuery(VersionHashVo versionHashVo) {
		PageHelper.startPage(versionHashVo.getPageNum(),versionHashVo.getPageSize());
		return new PageInfo<VersionHash>(findByQuery(versionHashVo));
	}
	@Override
	public List<VersionHash> findByQuery(VersionHashVo versionHashVo) {
		orderBy(versionHashVo.getOrderColumn() + " " + versionHashVo.getOrderValue());
		Example example=new Example(VersionHash.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件

		//查询版本类型
		if(!StringUtils.isEmpty(versionHashVo.getVersionType())){
			criteria.andEqualTo("versionType",versionHashVo.getVersionType());
		}
		//查询版本
		if(!StringUtils.isEmpty(versionHashVo.getVersionNumber())){
			criteria.andEqualTo("versionNumber",versionHashVo.getVersionNumber());
		}
		//example.createCriteria().andEqualTo("versionNumber", versionHashVo.getVersionNumber());
		example.orderBy("updateTime").desc();
		return versionHashMapper.selectByExample(example);
	}

}
