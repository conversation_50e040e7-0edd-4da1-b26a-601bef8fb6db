<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.VersionHashMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 【请填写功能名称】 -->
  <resultMap id="BaseResultMap" type="com.jh.sys.bean.VersionHash" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 版本号 -->
    <result column="Version_Number" property="versionNumber" jdbcType="VARCHAR" />
    <!-- 版本名称 -->
    <result column="Version_Type" property="versionType" jdbcType="INTEGER" />
    <!-- 版本jar包MD5哈希值 -->
    <result column="Version_Hash" property="versionHash" jdbcType="VARCHAR" />
    <!-- 版本说明 -->
    <result column="Version_Description" property="versionDescription" jdbcType="VARCHAR" />
  </resultMap>


    <!--查询最新版本 -->
    <select id="findByVersionHash" resultType="com.jh.sys.bean.VersionHash">
        SELECT *  FROM version_hash  ORDER BY CREATE_TIME DESC
            LIMIT 1
    </select>
</mapper>