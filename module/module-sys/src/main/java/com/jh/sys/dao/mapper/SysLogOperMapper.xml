<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysLogOperMapper">
	<resultMap id="CommonBaseResultMap"
		type="com.jh.common.bean.BaseEntity">
		<result column="ID" property="id" jdbcType="VARCHAR" />
		<result column="YN" property="yn" jdbcType="INTEGER" />
		<result column="CREATE_TIME" property="createTime"
			jdbcType="TIMESTAMP" />
		<result column="UPDATE_TIME" property="updateTime"
			jdbcType="TIMESTAMP" />
		<result column="CREATE_USER" property="createUser"
			jdbcType="VARCHAR" />
		<result column="UPDATE_USER" property="updateUser"
			jdbcType="VARCHAR" />
		<result column="CREATE_USER_NICKNAME"
			property="createUserNickname" jdbcType="VARCHAR" />
		<result column="UPDATE_USER_NICKNAME"
			property="updateUserNickname" jdbcType="VARCHAR" />
	</resultMap>
	<!-- 用户操作日志表 -->
	<resultMap id="BaseResultMap"
               type="com.jh.common.bean.SysLogOper" extends="CommonBaseResultMap">
		<!-- WARNING - @mbg.generated -->
		<!-- 操作一级模块 -->
		<result column="OPER_ONE_MODULE" property="operOneModule"
			jdbcType="VARCHAR" />
		<!-- 操作二级模块 -->
		<result column="OPER_TWO_MODULE" property="operTwoModule"
			jdbcType="VARCHAR" />
		<!-- 操作详细说明 -->
		<result column="OPER_DETAILS" property="operDetails"
			jdbcType="VARCHAR" />
		<!-- IP地址 -->
		<result column="IP" property="ip" jdbcType="VARCHAR" />
	</resultMap>
	<!-- 导出 -->
	<select id="selectExportData"
		resultType="com.jh.sys.bean.vo.SysLogOperExportVo">
		select * from sys_log_oper where yn=1
		<if
			test="sysLogOperVo.createUserNickname!=null and sysLogOperVo.createUserNickname!=''">
			<bind name="createUserNicknameSearch"
				value="'%'+sysLogOperVo.createUserNickname+'%'" />
			and CREATE_USER_NICKNAME like #{createUserNicknameSearch}
		</if>
		<if
			test="sysLogOperVo.operTwoModule!=null and sysLogOperVo.operTwoModule!=''">
			<bind name="operModuleSearch"
				value="'%'+sysLogOperVo.operTwoModule+'%'" />
			and (OPER_ONE_MODULE like #{operModuleSearch} or OPER_TWO_MODULE like
			#{operModuleSearch})
		</if>
		<if
			test="sysLogOperVo.operDetails!=null and sysLogOperVo.operDetails!=''">
			<bind name="operDetailSearch"
				value="'%'+sysLogOperVo.operDetails+'%'" />
			and OPER_DETAILS like #{operDetailSearch}
		</if> 
		<if test="sysLogOperVo.operTime!=null and sysLogOperVo.operTime.length == 2">
			<bind name="startTime" value="sysLogOperVo.operTime.get(0)"/>
			<bind name="endTime" value="sysLogOperVo.operTime.get(1)"/>
			and CREATE_TIME BETWEEN #{startTime} and #{endTime}
		</if>
		order by CREATE_TIME desc
	</select>
</mapper>