package com.jh.sys.bean.rest;

import java.io.Serializable;

/**
 * 单点登录得到的用户信息
 *
 * <AUTHOR>
 * @date 2020年4月13日
 */
//@ApiModel(value = "com.fd.itsp.beans.vo.OssUser", description = "单点登录得到的用户信息")
public class OssUser implements Serializable {


    /**
     * @Fields serialVersionUID :
     */

    private static final long serialVersionUID = 1L;
    //    @ApiModelProperty(value = "是否成功(成功：true 失败：false)")
    private String success;
    //    @ApiModelProperty(value = "手机号 用来关联业务系统")
    private String mobile;
    //    @ApiModelProperty(value = "结果详情")
    private String data;
    //    @ApiModelProperty(value = "省局中台UserId 重要字段，用来关联用户基础表")
    private String userId;

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


}
