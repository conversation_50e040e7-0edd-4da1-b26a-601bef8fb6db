package com.jh.sys.bean.vo;

import com.jh.sys.bean.SysMenu;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * 菜单表Vo
 *
 * <AUTHOR>
 * @date 2020-09-07 08:15:22
 */
@Schema(description = "菜单表Vo")
public class SysMenuVo extends SysMenu {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 是否是管理员
     */
    private boolean isAdmin;

    private List<SysMenuVo> children = new ArrayList<SysMenuVo>();

    public List<SysMenuVo> getChildren() {
        return children;
    }

    public void setChildren(List<SysMenuVo> children) {
        this.children = children;
    }

    public boolean isAdmin() {
        return isAdmin;
    }

    public void setAdmin(boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

}
