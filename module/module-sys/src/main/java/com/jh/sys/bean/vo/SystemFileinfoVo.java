package com.jh.sys.bean.vo;

import com.jh.common.bean.SysFileInfo;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 附件汇总表Vo
 *
 * <AUTHOR>
 * @date 2019-11-13 10:01:04
 */
@Schema(description = "附件汇总表Vo")
public class SystemFileinfoVo extends SysFileInfo {

    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    private String beginDate;
    private String endDate;
    //排序字段
    private String sortField;
    //排序类型
    private String sortType;

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

}
