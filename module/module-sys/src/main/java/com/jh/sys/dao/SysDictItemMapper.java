package com.jh.sys.dao;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sys.bean.SysDictItem;
import com.jh.sys.bean.vo.SysDictItemVo;
import com.jh.sys.bean.vo.TreeDropVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统数据字典分类
 *
 * <AUTHOR>
 * @date 2020-07-05 15:14:51
 */
public interface SysDictItemMapper extends BaseInfoMapper<SysDictItem> {

    /**
     * 根据字典CODE得到下级结点列表一级
     *
     * @param typeCode 字典编码
     * @return 结果
     */
    List<SysDictItem> findDictItemByDictType(@Param("typeCode") String typeCode);

    /**
     * 得到字典 treeDrop
     *
     * @param dictItem 字典对象
     * @return 结果
     */
    List<TreeDropVo> getTreeDrop(SysDictItem dictItem);

    /**
     * 根据字典CODE得到所有子结点
     *
     * @param vo 字典Vo
     * @return 结果
     */
    List<SysDictItem> findDictItemByLikeDictType(SysDictItemVo vo);

    /**
     * 根据ItemCode获取Item
     */
    SysDictItem findByItemCode(String itemCode);
}