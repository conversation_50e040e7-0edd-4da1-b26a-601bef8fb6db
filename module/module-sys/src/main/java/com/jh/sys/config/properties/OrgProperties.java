package com.jh.sys.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 企业用户配置属性
 */
@Component
@ConfigurationProperties(prefix = OrgProperties.PREFIX)
@Data
public class OrgProperties {

    public static final String PREFIX = "expert.org";

    /**
     * 默认密码
     */
    private String password;

    /**
     * 角色代码
     */
    private String roleCode;


    /**
     * 用户类型代码
     */
    private String userTypeCode;


}