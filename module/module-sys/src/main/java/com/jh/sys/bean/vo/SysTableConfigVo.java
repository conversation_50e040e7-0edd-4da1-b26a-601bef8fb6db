package com.jh.sys.bean.vo;

import com.jh.sys.bean.SysTableConfig;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 表格基础配置Vo
 *<AUTHOR>
 *@date 2023-09-14
 */
@Schema(description = "SysTableConfigVo")
public class SysTableConfigVo extends SysTableConfig {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}