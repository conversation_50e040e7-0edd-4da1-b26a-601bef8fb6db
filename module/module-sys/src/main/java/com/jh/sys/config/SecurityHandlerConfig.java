package com.jh.sys.config;

import com.alibaba.fastjson2.JSONObject;
import com.jh.common.annotation.OperLogAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.bean.ResponseInfo;
import com.jh.common.bean.SysUser;
import com.jh.common.bean.Token;
import com.jh.common.util.http.IpUtils;
import com.jh.common.util.http.ResponseUtil;
import com.jh.common.util.security.JwtUtils;
import com.jh.constant.TokenConstants;

import com.jh.sys.dao.SysUserMapper;
import com.jh.sys.service.LoginAttemptService;
import com.jh.sys.service.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * spring security处理器
 */
@Configuration
public class SecurityHandlerConfig {
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private LoginAttemptService loginAttemptService;
    /**
     * 登陆成功，返回Token
     *
     * @return
     */
    @Bean
    public AuthenticationSuccessHandler loginSuccessHandler(TokenService tokenService
            , SysUserMapper sysUserMapper, LoginAttemptService loginAttemptService) {
        return new AuthenticationSuccessHandler() {

            @Override
            @OperLogAnnotation(value = "'登录成功'", module = "用户登录", submodule = "用户登录")
            public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
                loginAttemptService.loginSucceeded(IpUtils.getIpAddr(request));
                LoginUser loginUser = (LoginUser) authentication.getPrincipal();
                loginResponse(loginUser, response);
            }
        };
    }

    public void loginResponse(LoginUser loginUser, HttpServletResponse response) {
        JSONObject tokenJson = generateToken(loginUser);
        ResponseUtil.responseJson(response, HttpStatus.OK.value(), tokenJson);
    }

    public JSONObject generateToken(LoginUser loginUser) {
        loginUser.setPassword(null);
        Token token = tokenService.saveToken(loginUser);
        loginUser.setToken(token.getToken());

        SysUser user = new SysUser();
        user.setId(loginUser.getId());
        user.setLastLogin(new Date());
        sysUserMapper.updateByPrimaryKeySelective(user);
        JSONObject tokenJson = JSONObject.parseObject(JSONObject.toJSONString(token));

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(TokenConstants.USER_KEY, token.getToken());
        claimsMap.put(TokenConstants.DETAILS_USER_ID, loginUser.getId());
        claimsMap.put(TokenConstants.DETAILS_USERNAME, loginUser.getUsername());
        claimsMap.put(TokenConstants.DETAILS_USERNICKNAME, loginUser.getNickname());
        claimsMap.put(TokenConstants.DETAILS_USERTYPE, loginUser.getType());

        tokenJson.put("token", JwtUtils.createToken(claimsMap));

        return tokenJson;
    }


    /**
     * 登陆失败
     *
     * @return
     */
    @Bean
    public AuthenticationFailureHandler loginFailureHandler(LoginAttemptService loginAttemptService) {
        return new AuthenticationFailureHandler() {
            @Override
            public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) {
                loginAttemptService.loginFailed(IpUtils.getIpAddr(request));
                String msg = null;
                if (exception instanceof BadCredentialsException) {
                    if("您的密码复杂度太低请修改密码后登录".equals(exception.getMessage())){
                        msg = "您的密码复杂度太低请修改密码后登录";
                    }else if("请绑定密钥".equals(exception.getMessage())){
                        msg = "请绑定密钥";
                    }else {
                        msg = "用户名或密码错误";
                    }
                } else {
                    msg = exception.getMessage();
                }
                ResponseInfo info = new ResponseInfo(HttpStatus.UNAUTHORIZED.value() + "", msg);
                ResponseUtil.responseJson(response, HttpStatus.UNAUTHORIZED.value(), info);
            }
        };

    }

    /**
     * 未登录，返回401
     *
     * @return
     */
    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return new AuthenticationEntryPoint() {

            @Override
            public void commence(HttpServletRequest request, HttpServletResponse response,
                                 AuthenticationException authException) throws IOException {
                if(response.getStatus() == HttpStatus.NOT_FOUND.value()){
                    ResponseInfo info = new ResponseInfo(HttpStatus.NOT_FOUND.value() + "", "404未找到该资源");
                    ResponseUtil.responseJson(response, HttpStatus.NOT_FOUND.value(), info);
                }else {
                    ResponseInfo info = new ResponseInfo(HttpStatus.UNAUTHORIZED.value() + "", "请先登录");
                    ResponseUtil.responseJson(response, HttpStatus.UNAUTHORIZED.value(), info);
                }
            }
        };
    }

    /**
     * 退出处理
     *
     * @return
     */
    @Bean
    public LogoutSuccessHandler logoutSussHandler(TokenService tokenService) {
        return new LogoutSuccessHandler() {

            @Override
            public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException {
                ResponseInfo info = new ResponseInfo(HttpStatus.OK.value() + "", "退出成功");

                String token = tokenService.getToken(request);
                tokenService.deleteToken(token);

                ResponseUtil.responseJson(response, HttpStatus.OK.value(), info);
            }
        };

    }

}
