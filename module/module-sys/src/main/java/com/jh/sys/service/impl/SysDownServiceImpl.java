package com.jh.sys.service.impl;

import com.github.pagehelper.PageHelper;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.LoginUser;
import com.jh.common.constant.CommonConstant;
import com.jh.common.exception.ServiceException;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sys.bean.SysDown;
import com.jh.sys.bean.vo.SysDownVo;
import com.jh.sys.dao.SysDownMapper;
import com.jh.sys.service.SysDownService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

/**
 *  我的导出Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-07
 */
@Service
@Transactional(readOnly = true)
public class SysDownServiceImpl implements SysDownService {

	private static final Logger logger = LoggerFactory.getLogger(SysDownServiceImpl.class);
    @Autowired
    private SysDownMapper sysDownMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 * 保存或更新我的导出
	 *@param sysDown 我的导出对象
	 *@return String 我的导出ID
	 *<AUTHOR>
	 */
	public String saveOrUpdateSysDown(SysDown sysDown) {
		if(sysDown==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(sysDown.getId())){
			//新增
			sysDown.setId(UUIDUtils.getUUID());
			sysDownMapper.insertSelective(sysDown);
		}else{
			//避免页面传入修改
			sysDown.setYn(null);
			sysDownMapper.updateByPrimaryKeySelective(sysDown);
		}
		return sysDown.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 删除我的导出
	 *@param ids void 我的导出ID
	 *<AUTHOR>
	 */
	public void deleteSysDown(List<String> ids) {
		for(String id:ids){
			//TODO 做判断后方能执行删除
			SysDown sysDown=sysDownMapper.selectByPrimaryKey(id);
			if(sysDown==null){
				throw new ServiceException("非法请求");
			}
			//逻辑删除
			SysDown temsysDown=new SysDown();
			temsysDown.setYn(CommonConstant.FLAG_NO);
			temsysDown.setId(sysDown.getId());
			sysDownMapper.updateByPrimaryKeySelective(temsysDown);
		}
	}

	/**
	 * 查询我的导出详情
	 *@param id
	 *@return SysDown
	 *<AUTHOR>
	 */
    @Override
	public SysDown findById(String id) {
		return sysDownMapper.selectByPrimaryKey(id);
	}


	/**
	 * 分页查询我的导出
	 *@param sysDownVo
	 *@return PageInfo<SysDown>
	 *<AUTHOR>
	 */
	@Override
	public PageInfo<SysDown> findPageByQuery(SysDownVo sysDownVo) {
		PageHelper.startPage(sysDownVo.getPageNum(),sysDownVo.getPageSize());
		Example example=new Example(SysDown.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("createUser", AppUserUtil.getCurrentUserName());
		//查询条件
		if (!StringUtils.isEmpty(sysDownVo.getDownTimeStart())) {
			criteria.andBetween("downTime", sysDownVo.getDownTimeStart()+" 00:00:00", sysDownVo.getDownTimeEnd()+" 23:59:59");
		}
		if (!StringUtils.isEmpty(sysDownVo.getModType())) {
			criteria.andLike("modType","%"+sysDownVo.getModType()+"%");
		}
		if (!StringUtils.isEmpty(sysDownVo.getSonMod())) {
			criteria.andLike("sonMod","%"+sysDownVo.getSonMod()+"%");
		}
		if (!StringUtils.isEmpty(sysDownVo.getFileName())) {
			criteria.andLike("fileName","%"+sysDownVo.getFileName()+"%");
		}
		LoginUser appLoginUser = (LoginUser) AppUserUtil.getLoginAppUser();
		if (appLoginUser != null) {
			criteria.andEqualTo("userId",appLoginUser.getId());
		}
		example.orderBy("createTime").desc();
		List<SysDown> sysDownList=sysDownMapper.selectByExample(example);

		return new PageInfo<SysDown>(sysDownList);
	}
}
