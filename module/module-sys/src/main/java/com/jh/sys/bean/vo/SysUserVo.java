package com.jh.sys.bean.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jh.common.bean.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;

import java.util.List;

/**
 * 系统用户表Vo
 *
 * <AUTHOR>
 * @date 2020-07-05 16:25:12
 */
@Schema(description = "系统用户表Vo")
@Data
public class SysUserVo extends SysUser {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    /**
     * 角色名称
     */
    @ExcelProperty("角色名称")
    private String roleName;
    /**
     * 工作单位 查询条件
     */
    private String deptName;
    /**
     * 用户角色
     */
    private String roleCode;
    /**
     * 用户姓名(真实姓名)
     */

    private String realName;
    /**
     * 多个角色
     */
    private List<String> roleIds;
    /**
     * 多个功能角色
     */
    private List<String> roleFunctionIds;
    /**
     * 部门名称
     */
    private String departmentName;
    /**
     * 功能角色
     */
    private String roleFunctionCode;
    /**
     * 功能角色名
     */
    private String roleFunctionName;
    /**
     * 角色id
     */
    private String displayRoleIds;
    /**
     * 功能角色id
     */
    private String displayRoleFuntionIds;
    /**
     * 新密码
     */
    private String newPwd;
    /**
     * 旧密码
     */
    private String oldPwd;

    /**
     * 验证码
     */
    private String code;
    /**
     * 验证码对应的uuid
     */
    private String uuid;
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 排序类型
     */
    private String sortType;
    /**
     * 员工信息传递过来id
     */
    private String userId;
    /**
     * 功能角色Code
     */
    private String displayRoleCodes;
    /**
     * 专家的信息
     */

    private String name;
    private String sex;
    private String idCardNumber;
    private String degree;
    private String post;
    private String titleName;
    private String unitName;
    private String birthday;
    /**
     * 多个用户
     */
    private List<String> userIds;

    /**
     * 一体化用户ID
     */
    @Column(name = "YTH_USER_ID")
    private String ythUserId;

    /**
     * 机构批准号
     */
    @Column(name = "HELPER_ORG_APPROVAL_NUM")
    private String helperOrgApprovalNum;

}
