package com.jh.sys.bean;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 系统数据字典分类
 *
 * <AUTHOR>
 * @date 2020-07-05 15:14:51
 */
@Table(name = "SYS_DICT_ITEM")
@Schema(description = "系统数据字典分类")
public class SysDictItem extends BaseEntity {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Column(name = "STATUS_TXT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "状态（启用,停用）")
    private String statusTxt;

    @Column(name = "STATUS")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "状态（1启用,0停用）")
    private Integer status;

    @Column(name = "ORDER_VAL")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "排序值")
    private Integer orderVal;

    @Column(name = "ITEM_DESC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "条目说明")
    private String itemDesc;

    @Column(name = "ITEM_VALUE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "业务编码")
    private String itemValue;

    @Column(name = "ITEM_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "条目编码")
    private String itemCode;

    @Column(name = "PARENT_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "父节点CODE")
    private String parentCode;

    @Column(name = "IS_LEAF")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "")
    private Integer isLeaf;

    /**
     * GET 状态（启用,停用）
     *
     * @return statusTxt
     */
    public String getStatusTxt() {
        return statusTxt;
    }

    /**
     * SET 状态（启用,停用）
     *
     * @param statusTxt
     */
    public void setStatusTxt(String statusTxt) {
        this.statusTxt = statusTxt == null ? null : statusTxt.trim();
    }

    /**
     * GET 状态（1启用,0停用）
     *
     * @return status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * SET 状态（1启用,0停用）
     *
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * GET 排序值
     *
     * @return orderVal
     */
    public Integer getOrderVal() {
        return orderVal;
    }

    /**
     * SET 排序值
     *
     * @param orderVal
     */
    public void setOrderVal(Integer orderVal) {
        this.orderVal = orderVal;
    }

    /**
     * GET 条目说明
     *
     * @return itemDesc
     */
    public String getItemDesc() {
        return itemDesc;
    }

    /**
     * SET 条目说明
     *
     * @param itemDesc
     */
    public void setItemDesc(String itemDesc) {
        this.itemDesc = itemDesc == null ? null : itemDesc.trim();
    }

    /**
     * GET 业务编码
     *
     * @return itemValue
     */
    public String getItemValue() {
        return itemValue;
    }

    /**
     * SET 业务编码
     *
     * @param itemValue
     */
    public void setItemValue(String itemValue) {
        this.itemValue = itemValue == null ? null : itemValue.trim();
    }

    /**
     * GET 条目编码
     *
     * @return itemCode
     */
    public String getItemCode() {
        return itemCode;
    }

    /**
     * SET 条目编码
     *
     * @param itemCode
     */
    public void setItemCode(String itemCode) {
        this.itemCode = itemCode == null ? null : itemCode.trim();
    }

    /**
     * GET 父节点CODE
     *
     * @return parentCode
     */
    public String getParentCode() {
        return parentCode;
    }

    /**
     * SET 父节点CODE
     *
     * @param parentCode
     */
    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    /**
     * GET
     *
     * @return isLeaf
     */
    public Integer getIsLeaf() {
        return isLeaf;
    }

    /**
     * SET
     *
     * @param isLeaf
     */
    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}