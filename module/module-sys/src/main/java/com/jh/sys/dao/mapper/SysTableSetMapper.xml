<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysTableSetMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 用户格设置 -->
  <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysTableSet" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 用户ID -->
    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <!-- 表标识 -->
    <result column="TABLE_FLAG" property="tableFlag" jdbcType="VARCHAR" />
    <!-- 表描述 -->
    <result column="TABLE_DESC" property="tableDesc" jdbcType="VARCHAR" />
    <!-- 列表字段 -->
    <result column="LIST_FIELD" property="listField" jdbcType="VARCHAR" />
    <!-- 导出字段 -->
    <result column="EXP_FIELD" property="expField" jdbcType="VARCHAR" />
  </resultMap>

</mapper>