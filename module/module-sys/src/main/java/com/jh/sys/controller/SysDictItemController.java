package com.jh.sys.controller;

import com.jh.common.annotation.OperLogAnnotation;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysDictItem;
import com.jh.sys.bean.vo.DictTree;
import com.jh.sys.bean.vo.SysDictVo;
import com.jh.sys.service.SysDictItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统数据字典分类
 *
 * <AUTHOR>
 * @date 2020-07-05 15:14:51
 */
@RestController
@RequestMapping("/sys/dictItem")
@Tag(name = "系统数据字典分类")
public class SysDictItemController extends BaseController {

    private static final String PREFIX = "btn:sys:dict:";
    @Autowired
    private SysDictItemService basicDictItemService;

    /**
     * 初始化字典数
     *
     * @return List<DictTree>
     */
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
      @Operation(summary="查询属性字典")
    @PreAuthorize("hasAuthority('" + PREFIX + "tree')")
    public List<DictTree> getTree() {
        List<DictTree> dictTree = basicDictItemService.getDictTree(isAdmin());
        return dictTree;
    }

    /**
     * 新增系统数据字典分类
     *
     * @param basicDictItem 系统数据字典分类数据 json
     * @return RestApiResponse<?>
     */

    @RepeatSubAnnotation
    @PostMapping("/save")
      @Operation(summary="新增系统数据字典分类")
    @SystemLogAnnotation(type = "系统数据字典分类", value = "新增系统数据字典分类")
    @OperLogAnnotation(value = "'新增系统数据字典分类，字典code：'+#basicDictItem.itemCode", module = "基础信息管理", submodule = "字典管理")
    @PreAuthorize("hasAuthority('" + PREFIX + "add')")
    public RestApiResponse<?> addBasicDictItem(@RequestBody SysDictItem basicDictItem) {
        SysDictVo dictVo = new SysDictVo();
        dictVo.setWhetherAdmin(isAdmin());
        basicDictItemService.addBasicDictItem(basicDictItem, dictVo);
        return RestApiResponse.ok(basicDictItem);
    }

    /**
     * 修改系统数据字典分类
     *
     * @param basicDictItem 系统数据字典分类数据 json
     * @return RestApiResponse<?>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
      @Operation(summary="修改系统数据字典分类")
    @SystemLogAnnotation(type = "系统数据字典分类", value = "修改系统数据字典分类")
    @OperLogAnnotation(value = "'修改系统数据字典分类，字典code：'+#basicDictItem.itemCode", module = "基础信息管理", submodule = "字典管理")
    @PreAuthorize("hasAuthority('" + PREFIX + "update')")
    public RestApiResponse<?> update(@RequestBody SysDictItem basicDictItem) {
        basicDictItemService.updateDictItem(basicDictItem, isAdmin());
        return RestApiResponse.ok(basicDictItem);
    }

    /**
     * 删除数据字典分类（新）
     *
     * @param id 系统数据字典分类ID
     * @return void
     */
    @PostMapping(value = "/delete/{id}")
      @Operation(summary="删除字典")
    @ResponseBody
    @SystemLogAnnotation(type = "字典管理", value = "删除字典")
    @OperLogAnnotation(value = "'删除字典，id:'+#id", module = "基础信息管理", submodule = "字典管理")
    @PreAuthorize("hasAuthority('" + PREFIX + "delete')")
    public RestApiResponse<?> deleteById(@PathVariable("id") String id) {
        basicDictItemService.deleteById(id);
        return RestApiResponse.ok();
    }

    /**
     * 字典单条查询
     *
     * @param id 系统数据字典分类数据 json
     * @return RestApiResponse<?>
     */
    @PostMapping(value = "/findId/{id}")
      @Operation(summary="字典单条查询")
    @ResponseBody
    @PreAuthorize("hasAuthority('" + PREFIX + "id')")
    public RestApiResponse<?> findId(@PathVariable("id") String id) {
        SysDictItem dictItem = basicDictItemService.findById(id);
        return RestApiResponse.ok(dictItem);
    }
}
