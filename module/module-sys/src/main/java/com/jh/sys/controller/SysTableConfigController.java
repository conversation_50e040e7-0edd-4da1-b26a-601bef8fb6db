package com.jh.sys.controller;

import com.alibaba.fastjson2.JSONArray;
import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysTableConfig;
import com.jh.sys.bean.vo.SysTableConfigVo;
import com.jh.sys.service.SysTableConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 表格基础配置Controller
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@RestController
@RequestMapping("/sys/tableconfig")
@Tag(name= "表格基础配置")
public class SysTableConfigController extends BaseController {
    @Autowired
    private SysTableConfigService sysTableConfigService;

    private static final String PER_PREFIX = "btn:sys:tableconfig:";

    /**
     * 新增表格基础配置
     *
     * @param sysTableConfig 表格基础配置数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
      @Operation(summary="新增表格基础配置")
    @SystemLogAnnotation(type = "表格基础配置", value = "新增表格基础配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveSysTableConfig(@RequestBody SysTableConfig sysTableConfig) {
        String id = sysTableConfigService.saveOrUpdateSysTableConfig(sysTableConfig);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改表格基础配置
     *
     * @param sysTableConfig 表格基础配置数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
      @Operation(summary="修改表格基础配置")
    @SystemLogAnnotation(type = "表格基础配置", value = "修改表格基础配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateSysTableConfig(@RequestBody SysTableConfig sysTableConfig) {
        String id = sysTableConfigService.saveOrUpdateSysTableConfig(sysTableConfig);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除表格基础配置(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
      @Operation(summary="批量删除表格基础配置")
    @SystemLogAnnotation(type = "表格基础配置", value = "批量删除表格基础配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteSysTableConfig(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        sysTableConfigService.deleteSysTableConfig(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询表格基础配置详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
      @Operation(summary="查询表格基础配置详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        SysTableConfig sysTableConfig = sysTableConfigService.findById(id);
        return RestApiResponse.ok(sysTableConfig);
    }

    /**
     * 分页查询表格基础配置
     *
     * @param sysTableConfigVo 表格基础配置 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
      @Operation(summary="分页查询表格基础配置")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody SysTableConfigVo sysTableConfigVo) {
        PageInfo<SysTableConfig> sysTableConfig = sysTableConfigService.findPageByQuery(sysTableConfigVo);
        return RestApiResponse.ok(sysTableConfig);
    }


    @GetMapping("/findByFlag")
      @Operation(summary="查询表格基础配置详情")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"findByFlag')")
    public RestApiResponse<?> findByFlag(@RequestParam("tableFlag") String tableFlag) {
        JSONArray sysTableConfig = sysTableConfigService.findExcelJsonByFlag(tableFlag);
        return RestApiResponse.ok(sysTableConfig);
    }
}
