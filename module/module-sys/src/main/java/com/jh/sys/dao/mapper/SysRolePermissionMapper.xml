<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sys.dao.SysRolePermissionMapper">
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="YN" property="yn" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 角色权限 -->
    <resultMap id="BaseResultMap" type="com.jh.sys.bean.SysRolePermission" extends="CommonBaseResultMap">
        <!-- WARNING - @mbg.generated -->
        <!-- 排序值 -->
        <result column="ORDER_VAL" property="orderVal" jdbcType="INTEGER"/>
        <!-- 角色编码 -->
        <result column="ROLE_CODE" property="roleCode" jdbcType="VARCHAR"/>
        <!-- 资源ID -->
        <result column="RESOURCE_ID" property="resourceId" jdbcType="VARCHAR"/>
        <!-- 资源类型 button=按钮 menu=菜单 -->
        <result column="RESOURCE_TYPE" property="resourceType" jdbcType="VARCHAR"/>
    </resultMap>
    <delete id="deletePermissionByRoleCode">
        <if test="resourceType !=null and resourceType=='menu'">
            delete from sys_role_permission where Role_Code =
            #{roleCode} and
            Resource_Type = #{resourceType}
        </if>
        <if test="resourceType !=null and resourceType=='button'">
            delete from sys_role_permission where Role_Code = #{roleCode} and Resource_Type = #{resourceType}
                                            and Resource_Id IN(SELECT e.ID FROM sys_Element e WHERE e.Menu_Id = #{menuId})
        </if>
    </delete>

    <select id="findMenuPermissionCodeByRoleId" resultType="String">
        select distinct m.Code from sys_Role r right join sys_role_permission
        p on r.Role_Code = p.Role_Code right join sys_Menu m on m.ID =
        p.Resource_Id where r.status=1 and r.ID in
        <foreach collection="roleIds" item="roleId" index="index"
                 open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>

    <select id="findElementPermissionCodeByRoleId" resultType="String">
        select DISTINCT e.PERMS from sys_Menu e ,sys_role_permission r
        where e.id=r.RESOURCE_ID
        and EXISTS (
        select 1 from sys_Role where id in
        <foreach collection="roleIds" item="roleId" index="index"
                 open="(" close=")" separator=",">
            #{roleId}
        </foreach>
        and r.ROLE_CODE =ROLE_CODE
        )
        and e.PERMS is not null
    </select>


</mapper>