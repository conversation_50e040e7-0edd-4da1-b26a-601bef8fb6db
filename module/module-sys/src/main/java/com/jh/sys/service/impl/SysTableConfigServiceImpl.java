package com.jh.sys.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.CommonConstant;
import com.jh.common.service.BaseServiceImpl;
import com.jh.sys.bean.SysTableConfig;
import com.jh.sys.bean.vo.SysTableConfigVo;
import com.jh.common.exception.ServiceException;
import com.jh.sys.dao.SysTableConfigMapper;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sys.service.SysTableConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 表格基础配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Service
@Transactional(readOnly = true)
public class SysTableConfigServiceImpl extends BaseServiceImpl<SysTableConfigMapper, SysTableConfig> implements SysTableConfigService {

    private static final Logger logger = LoggerFactory.getLogger(SysTableConfigServiceImpl.class);
    @Autowired
    private SysTableConfigMapper sysTableConfigMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新表格基础配置
     *@param sysTableConfig 表格基础配置对象
     *@return String 表格基础配置ID
     *<AUTHOR>
     */
    public String saveOrUpdateSysTableConfig(SysTableConfig sysTableConfig) {
        if (sysTableConfig == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(sysTableConfig.getId())) {
            //新增
            sysTableConfig.setId(UUIDUtils.getUUID());
            sysTableConfigMapper.insertSelective(sysTableConfig);
        } else {
            //避免页面传入修改
            sysTableConfig.setYn(null);
            sysTableConfigMapper.updateByPrimaryKeySelective(sysTableConfig);
        }
        return sysTableConfig.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除表格基础配置
     *@param ids void 表格基础配置ID
     *<AUTHOR>
     */
    public void deleteSysTableConfig(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            SysTableConfig sysTableConfig = sysTableConfigMapper.selectByPrimaryKey(id);
            if (sysTableConfig == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            SysTableConfig temsysTableConfig = new SysTableConfig();
            temsysTableConfig.setYn(CommonConstant.FLAG_NO);
            temsysTableConfig.setId(sysTableConfig.getId());
            sysTableConfigMapper.updateByPrimaryKeySelective(temsysTableConfig);
        }
    }

    /**
     * 查询表格基础配置详情
     *
     * @param id
     * @return SysTableConfig
     * <AUTHOR>
     */
    @Override
    public SysTableConfig findById(String id) {
        return sysTableConfigMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询表格基础配置
     *
     * @param sysTableConfigVo
     * @return PageInfo<SysTableConfig>
     * <AUTHOR>
     */
    @Override
    public PageInfo<SysTableConfig> findPageByQuery(SysTableConfigVo sysTableConfigVo) {
        PageHelper.startPage(sysTableConfigVo.getPageNum(), sysTableConfigVo.getPageSize());
        orderBy(sysTableConfigVo.getOrderColumn() + " " + sysTableConfigVo.getOrderValue());
        Example example = new Example(SysTableConfig.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        if(!StringUtils.isEmpty(sysTableConfigVo.getTableFlag())){
        	criteria.andLike("tableFlag","%" + sysTableConfigVo.getTableFlag() + "%"  );
        }
        if(!StringUtils.isEmpty(sysTableConfigVo.getModType())){
            criteria.andLike("modType","%" + sysTableConfigVo.getModType() + "%"  );
        }
        if(!StringUtils.isEmpty(sysTableConfigVo.getSonMod())){
            criteria.andLike("sonMod","%" + sysTableConfigVo.getSonMod() + "%"  );
        }
        if(!StringUtils.isEmpty(sysTableConfigVo.getTableName())){
            criteria.andLike("tableName","%" + sysTableConfigVo.getTableName() + "%"  );
        }
        example.orderBy("updateTime").desc();
        List<SysTableConfig> sysTableConfigList = sysTableConfigMapper.selectByExample(example);
        return new PageInfo<SysTableConfig>(sysTableConfigList);
    }

    @Override
    public JSONArray findExcelJsonByFlag(String tableFlag) {
        SysTableConfig sysTableConfig = new SysTableConfig();
        sysTableConfig.setTableFlag(tableFlag);
        sysTableConfig.setYn(CommonConstant.FLAG_YES);
        List<SysTableConfig> select = mapper.select(sysTableConfig);
        if (CollectionUtils.isEmpty(select)) {

            return new JSONArray();
        }
        String exportJson = select.get(0).getExportJson();
        return JSONArray.parseArray(exportJson);
    }

    @Override
    public JSONArray findListJsonByFlag(String tableFlag) {
        SysTableConfig sysTableConfig = new SysTableConfig();
        sysTableConfig.setTableFlag(tableFlag);
        sysTableConfig.setYn(CommonConstant.FLAG_YES);
        List<SysTableConfig> select = mapper.select(sysTableConfig);
        if (CollectionUtils.isEmpty(select)) {

            return new JSONArray();
        }
        String showJson = select.get(0).getShowJson();
        return JSONArray.parseArray(showJson);
    }
}
