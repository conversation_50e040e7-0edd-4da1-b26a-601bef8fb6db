package com.jh.sys.controller;

import com.alibaba.fastjson2.JSONObject;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sys.bean.SysMenu;
import com.jh.sys.bean.vo.SysMenuVo;
import com.jh.sys.bean.vo.TreeSelect;
import com.jh.sys.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys/menu")
@Tag(name = "菜单接口")
public class MenuController extends BaseController {

    private static final String prefix = "btn:sys:fun:menu:";

    @Autowired
    private MenuService menuService;

    @RepeatSubAnnotation
    @PostMapping("/add")
      @Operation(summary="新增菜单")
    @PreAuthorize("hasAuthority('" + prefix + "add')")
    public RestApiResponse<?> add(@RequestBody SysMenuVo vo) {
        vo.setAdmin(isAdmin());
        // 如果是按钮类型(menuType为F)且path为空，则设置默认path为#
        if ("F".equals(vo.getMenuType()) && (vo.getPath() == null || vo.getPath().trim().isEmpty())) {
            vo.setPath("#");
        }
        this.menuService.addSysMenu(vo);
        return RestApiResponse.ok(1);
    }

    @RepeatSubAnnotation
    @PutMapping(value = "/edit")
      @Operation(summary="修改菜单")
    @PreAuthorize("hasAuthority('" + prefix + "edit')")
    public RestApiResponse<?> update(@RequestBody SysMenuVo vo) {
        vo.setAdmin(isAdmin());

        if (vo.getId().equals(vo.getParentId())) {
            return RestApiResponse.error("修改菜单'" + vo.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        this.menuService.updateById(vo);
        return RestApiResponse.ok(vo);
    }

    @GetMapping(value = "/{menuId}")
      @Operation(summary="菜单单条查询")
    @PreAuthorize("hasAuthority('" + prefix + "byid')")
    public RestApiResponse<?> getInfo(@PathVariable("menuId") String menuId) {
        SysMenu sysMenu = this.menuService.selectById(menuId);
        return RestApiResponse.ok(sysMenu);
    }

    @RepeatSubAnnotation
    @DeleteMapping(value = "/{menuId}")
      @Operation(summary="删除菜单")
    @PreAuthorize("hasAuthority('" + prefix + "delete')")
    public RestApiResponse<?> remove(@PathVariable("menuId") String menuId) {
        this.menuService.deleteMenuById(menuId);
        return RestApiResponse.ok();
    }

    @PostMapping("/list")
      @Operation(summary="获取菜单列表")
    @PreAuthorize("hasAuthority('" + prefix + "list')")
    public RestApiResponse<?> list(@RequestBody SysMenuVo menu) {
        menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, getCurrentUserId());
        return RestApiResponse.ok(menus);
    }

    @GetMapping("/getRouters")
      @Operation(summary="获取菜单路由列表")
    public RestApiResponse<?> getRouters() {
        String userId = getCurrentUserId();
        SysMenuVo menu = new SysMenuVo();
        menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, userId);
        List<SysMenuVo> menusTree = menuService.getChildPerms(menus, "0");
        return RestApiResponse.ok(menuService.buildMenus(menusTree));
    }

    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
      @Operation(summary="角色查询菜单")
    @PreAuthorize("hasAuthority('" + prefix + "menuByRoleId')")
    public RestApiResponse<?> roleMenuTreeselect(@PathVariable("roleId") String roleId) {
        //角色对应的菜单
        List<SysMenu> sysMenuList = menuService.findRoleId(roleId);
        List<String> sysMenuStrs = sysMenuList.stream().map(menu -> menu.getId()).collect(Collectors.toList());

        SysMenuVo menu = new SysMenuVo();
        String userId = getCurrentUserId();
        menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, userId);

        List<TreeSelect> treeSelect = menuService.buildMenuTreeSelect(menus);
        JSONObject json = new JSONObject();
        json.put("checkedKeys", sysMenuStrs);
        json.put("menus", treeSelect);
        return RestApiResponse.ok(json);
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselect")
    public RestApiResponse<?> selectTree(SysMenuVo menu) {
        String userId = getCurrentUserId();
        menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, userId);
        return RestApiResponse.ok(menuService.buildMenuTreeSelect(menus));
    }
}
