package com.jh.sys.service;

import com.github.pagehelper.PageInfo;
import com.jh.sys.bean.VersionHash;
import com.jh.sys.bean.vo.VersionHashVo;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface VersionHashService{
	/**
	 * 保存或更新【新增版本信息】
	 *@param versionHash 【新增版本信息】对象
	 *@return String 【返回新增版本状态】ID
	 *<AUTHOR>
	 */
	String saveOrUpdateVersionHash(VersionHash versionHash) throws IOException, NoSuchAlgorithmException;
	

	/**
	 * 分页查询【版本列表】
	 *@param versionHashVo
	 *@return PageInfo<VersionHash>
	 *<AUTHOR>
	 */
	PageInfo<VersionHash> findPageByQuery(VersionHashVo versionHashVo);


	/**
     * 查询【最新一个版本】详情
     *
     * @param
     * @return VersionHash
     * <AUTHOR>
     */
	VersionHash findByVersionHash();

	List<VersionHash> findByQuery(VersionHashVo versionHashVo);
}
