package com.jh.sys.service;

import com.github.pagehelper.PageInfo;
import com.jh.common.bean.SysLogRecord;
import com.jh.common.service.LogService;
import com.jh.sys.bean.vo.SysLogRecordExportVo;
import com.jh.sys.bean.vo.SysLogRecordVo;

import java.util.List;

/**
 * 系统日志
 *
 * <AUTHOR>
 * @date 2019-11-04 14:33:24
 */
public interface SysLogService extends LogService {


    /**
     * 查询日志记录
     *
     * @param id 日志ID
     * @return SysLogRecord
     */
    SysLogRecord findById(String id);

    /**
     * 分页查询所有日志
     *
     * @param logVo 日志Vo
     * @return PageInfo<LogRecord>
     */
    PageInfo<SysLogRecord> findPageByQuery(SysLogRecordVo logVo);

    /**
     * 导出日志信息
     *
     * @param sysLogRecordVo 日志Vo
     * @return List<SysLogRecordExportVo>
     */
    List<SysLogRecordExportVo> export(SysLogRecordVo sysLogRecordVo);
}
