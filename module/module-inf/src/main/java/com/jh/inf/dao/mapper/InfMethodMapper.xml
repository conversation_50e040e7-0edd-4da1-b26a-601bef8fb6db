<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.inf.dao.InfMethodMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 应用令牌权限 -->
  <resultMap id="BaseResultMap" type="com.jh.inf.bean.InfMethod" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- 方法名称 -->
    <result column="METHOD" property="method" jdbcType="VARCHAR" />
    <!-- 接口方法状态：1代表正常，0代表挂起 -->
    <result column="STATUS" property="status" jdbcType="INTEGER" />
    <!-- 接口方法状态文本值：1代表正常，0代表挂起 -->
    <result column="STATUS_TXT" property="statusTxt" jdbcType="VARCHAR" />
    <!-- 方法说明 -->
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
  </resultMap>
</mapper>
