<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.inf.dao.CommonMapper" >
  
  
  <select id="commonSql" resultType="com.alibaba.fastjson2.JSONObject" >
  ${myQsql}
  </select>
  
  <select id="findCodeSql" resultType="String">
  select SQL_CODE from inf_sql_code where code=#{code} and yn =1
  </select>
  <select id="findInterface" resultType="com.jh.inf.bean.InfSqlCode">
    select * from inf_sql_code where code=#{code} and yn =1
  </select>
</mapper>