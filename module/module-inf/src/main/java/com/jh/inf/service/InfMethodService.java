package com.jh.inf.service;


import com.github.pagehelper.PageInfo;
import com.jh.inf.bean.InfMethod;
import com.jh.inf.bean.vo.InfMethodVo;
import com.jh.common.service.BaseService;

import java.util.List;

/**
 * 应用令牌权限Service接口
 *
 * <AUTHOR>
 * @date 2022-03-01 14:06:26
 */
public interface InfMethodService extends BaseService<InfMethod> {
	/**
	 *@Description: 保存或更新应用令牌权限
	 *@param infMethod 应用令牌权限对象
	 *@return String 应用令牌权限ID
	 *@Author: linqiang
	 */
	String saveOrUpdate(InfMethod infMethod);

	List<InfMethod> findAll();
	/**
	 *@Description: 删除应用令牌权限
	 *@param ids void 应用令牌权限ID
	 *@Author: linqiang
	 */
	void delete(List<String> ids);

	/**
	 *@Description: 查询应用令牌权限详情
	 *@param id
	 *@return InfMethod
	 *@Author: linqiang
	 */
	InfMethod findById(String id);

	/**
	 *@Description: 分页查询应用令牌权限
	 *@param infMethodVo
	 *@return PageInfo<InfMethod>
	 *@Author: linqiang
	 */
	PageInfo<InfMethod> findPageByQuery(InfMethodVo infMethodVo);
}
