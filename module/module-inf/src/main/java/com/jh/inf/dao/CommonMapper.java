package com.jh.inf.dao;

import com.alibaba.fastjson2.JSONObject;
import com.jh.common.mybatis.SelectDynamicMapper;
import com.jh.inf.bean.InfSqlCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CommonMapper extends SelectDynamicMapper {

	List<JSONObject> commonSql(@Param("obj")JSONObject obj, @Param("myQsql")String sql);

	List<String> findCodeSql(@Param("code") String code);
	List<InfSqlCode> findInterface(@Param("code") String code);
}
