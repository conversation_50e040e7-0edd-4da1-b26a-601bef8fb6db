package com.jh.inf.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.inf.bean.InfMethod;
import com.jh.inf.bean.vo.InfMethodVo;
import com.jh.inf.service.InfMethodService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用令牌权限Controller
 *
 * <AUTHOR>
 * @date 2022-03-01 14:06:26
 */
@RestController
@RequestMapping("/inf/method")
@Tag(name = "应用令牌权限")
public class InfMethodController extends BaseController {
    @Autowired
    private InfMethodService infMethodService;

    private static final String PER_PREFIX = "btn:inf:method:";

    /**
     * @param infMethod 应用令牌权限数据 json
     * @return RestApiResponse<?>
     * @Description: 新增应用令牌权限
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
      @Operation(summary="新增应用令牌权限")
    @SystemLogAnnotation(type = "应用令牌权限", value = "新增应用令牌权限")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> save(@RequestBody InfMethod infMethod) {
        String id = infMethodService.saveOrUpdate(infMethod);
        return RestApiResponse.ok(id);
    }

    /**
     * @param infMethod 应用令牌权限数据 json
     * @return RestApiResponse<?>
     * @Description: 修改应用令牌权限
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
      @Operation(summary="修改应用令牌权限")
    @SystemLogAnnotation(type = "应用令牌权限", value = "修改应用令牌权限")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> update(@RequestBody InfMethod infMethod) {
        String id = infMethodService.saveOrUpdate(infMethod);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除应用令牌权限(判断 关联数据是否可以删除)
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
      @Operation(summary="批量删除应用令牌权限")
    @SystemLogAnnotation(type = "应用令牌权限", value = "批量删除应用令牌权限")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> delete(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        infMethodService.delete(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询应用令牌权限详情
     * @Author: linqiang
     */
    @GetMapping("/findById")
      @Operation(summary="查询应用令牌权限详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        InfMethod infMethod = infMethodService.findById(id);
        return RestApiResponse.ok(infMethod);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 查询所有令牌权限
     * @Author: linqiang
     */
    @GetMapping("/findAll")
      @Operation(summary="查询所有令牌权限")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findAll')")
    public RestApiResponse<?> findAll() {
        List<InfMethod> infMethod = infMethodService.findAll();
        return RestApiResponse.ok(infMethod);
    }

    /**
     * @param infMethodVo 应用令牌权限 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询应用令牌权限
     * @Author: linqiang
     */
    @PostMapping("/findPageByQuery")
      @Operation(summary="分页查询应用令牌权限")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody InfMethodVo infMethodVo) {
        PageInfo<InfMethod> infMethod = infMethodService.findPageByQuery(infMethodVo);
        return RestApiResponse.ok(infMethod);
    }

}
