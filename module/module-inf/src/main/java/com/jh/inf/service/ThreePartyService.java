package com.jh.inf.service;


import com.alibaba.fastjson2.JSONArray;
import com.jh.inf.bean.InfApp;
import com.jh.inf.bean.InfTask;
import com.jh.inf.bean.vo.PlatformRequestVo;
import jakarta.validation.Valid;

import java.util.List;

public interface ThreePartyService {

	InfApp checkRequest(@Valid PlatformRequestVo request);

	/**
	 * 插入数据库提交事务并返回影响行数
	 * @param data
	 * @param taskList
	 * @return
	 */
	int execMethodTask(JSONArray data, List<InfTask> taskList);
}
