package com.jh.inf.bean.vo;

import com.jh.inf.bean.InfSqlCode;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 *@Description: API接口配置Vo
 *@Author: linqiang
 *@Date: 2022-08-01
 */
@Schema(description = "InfSqlCodeVo")
public class InfSqlCodeVo extends InfSqlCode {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}