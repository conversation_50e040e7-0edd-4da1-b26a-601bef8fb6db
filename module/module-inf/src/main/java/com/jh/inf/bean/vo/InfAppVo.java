package com.jh.inf.bean.vo;


import com.jh.inf.bean.InfApp;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 *@Description: 应用令牌秘钥配置Vo
 *@Author: l<PERSON><PERSON><PERSON>
 *@Date: 2022-03-01 12:54:48
 */
@Schema(description = "InfAppVo")
public class InfAppVo extends InfApp {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}
