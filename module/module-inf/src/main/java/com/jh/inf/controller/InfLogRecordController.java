package com.jh.inf.controller;


import com.github.pagehelper.PageInfo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.inf.bean.InfLogRecord;
import com.jh.inf.bean.vo.InfLogRecordVo;
import com.jh.inf.service.InfLogRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接口日志记录Controller
 *
 * <AUTHOR>
 * @date 2022-03-01 12:54:48
 */
@RestController
@RequestMapping("/inf/log")
@Tag(name ="接口日志记录")
public class InfLogRecordController extends BaseController {
    @Autowired
    private InfLogRecordService infLogRecordService;

	private static final String PER_PREFIX = "btn:inf:log:";

//	/**
//	 *@Description: 查询接口日志记录详情
//	 *@param id
//	 *@return RestApiResponse<?>
//	 *@Author: linqiang
//	 */
//	@GetMapping("/findById")
//	  @Operation(summary="查询接口日志记录详情")
//	@PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
//	public RestApiResponse<?> findById(@RequestParam("id") String id) {
//		InfLogRecord infLogRecord=infLogRecordService.findById(id);
//		return RestApiResponse.ok(infLogRecord);
//	}

	/**
	 *@Description: 分页查询接口日志记录
	 *@param infLogRecordVo 接口日志记录 查询条件
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@PostMapping("/findPageByQuery")
	  @Operation(summary="分页查询接口日志记录")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InfLogRecordVo infLogRecordVo) {
		PageInfo<InfLogRecord> infLogRecord=infLogRecordService.findPageByQuery(infLogRecordVo);
		return RestApiResponse.ok(infLogRecord);
	}

}
