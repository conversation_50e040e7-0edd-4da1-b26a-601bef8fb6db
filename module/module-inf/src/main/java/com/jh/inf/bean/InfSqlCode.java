package com.jh.inf.bean;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jh.common.bean.BaseEntity;
import com.jh.common.xss.StringFWBXssDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * API接口配置对象 inf_sql_code

 *
 * <AUTHOR>
 * @date 2022-08-01
 */
@Table(name = "inf_sql_code")
@Schema(description = "API接口配置")
public class InfSqlCode extends BaseEntity {
    private static final long serialVersionUID = 1L;

	@Column(name = "API")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="API调用说明")
	@JsonDeserialize(using = StringFWBXssDeserializer.class)
	private String api;
	@Column(name = "CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="接口标识")
	private String code;
	@Column(name = "SQL_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="sql语句")
	@JsonDeserialize(using = StringFWBXssDeserializer.class)
	private String sqlCode;
	@Column(name = "TYPE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="类型编码")
	private String typeCode;
	@Column(name = "README")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="sql说明")
	@JsonDeserialize(using = StringFWBXssDeserializer.class)
	private String readme;
    @Column(name = "CACHE_FLAG")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="是否缓存 1:是 0:否")
    private Integer cacheFlag;
    @Column(name = "CACHE_EXIST_TIME")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description ="缓存时间(单位：秒)")
    private Integer cacheExistTime;
	@Column(name = "TYPE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description ="类型名称")
	private String typeName;

    public Integer getCacheFlag() {
        return cacheFlag;
    }

    public void setCacheFlag(Integer cacheFlag) {
        this.cacheFlag = cacheFlag;
    }

    public Integer getCacheExistTime() {
        return cacheExistTime;
    }

    public void setCacheExistTime(Integer cacheExistTime) {
        this.cacheExistTime = cacheExistTime;
    }
    /**
     * SET API调用说明
     * @param api
     */
    public void setApi(String api){
		this.api = api == null ? null :api;
	}
    /**
     * GET API调用说明
     * @return api
     */
    public String getApi(){
        return api;
    }
    /**
     * SET 接口标识
     * @param code
     */
    public void setCode(String code){
		this.code = code == null ? null :code;
	}
    /**
     * GET 接口标识
     * @return code
     */
    public String getCode(){
        return code;
    }
    /**
     * SET sql语句
     * @param sqlCode
     */
    public void setSqlCode(String sqlCode){
		this.sqlCode = sqlCode == null ? null :sqlCode;
	}
    /**
     * GET sql语句
     * @return sqlCode
     */
    public String getSqlCode(){
        return sqlCode;
    }
    /**
     * SET 类型编码
     * @param typeCode
     */
    public void setTypeCode(String typeCode){
		this.typeCode = typeCode == null ? null :typeCode;
	}
    /**
     * GET 类型编码
     * @return typeCode
     */
    public String getTypeCode(){
        return typeCode;
    }
    /**
     * SET sql说明
     * @param readme
     */
    public void setReadme(String readme){
		this.readme = readme == null ? null :readme;
	}
    /**
     * GET sql说明
     * @return readme
     */
    public String getReadme(){
        return readme;
    }
    /**
     * SET 类型名称
     * @param typeName
     */
    public void setTypeName(String typeName){
		this.typeName = typeName == null ? null :typeName;
	}
    /**
     * GET 类型名称
     * @return typeName
     */
    public String getTypeName(){
        return typeName;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
