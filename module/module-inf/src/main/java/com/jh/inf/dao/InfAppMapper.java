package com.jh.inf.dao;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.inf.bean.InfApp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
*@Description: 应用令牌秘钥配置Mapper接口
*@Author: lin<PERSON><PERSON>
*@Date: 2022-03-01 12:54:48
*/
public interface InfAppMapper extends BaseInfoMapper<InfApp> {

   /**
    * 查询appkey 是否有权限
    * @param appKey
    * @param method
    * @return
    */
   int selectCountByAppKeyMethod(@Param("appKey") String appKey, @Param("method") String method);

   /**
    * 批量插入 appkey--method
    * @param methodList
    * @return
    */
   int insertAppKeyMethodList(List<Map<String, String>> methodList);

   int updateAppKey(@Param("newAppKey") String newAppKey, @Param("appKey") String appKey);
   int updateAppMethod(@Param("newAppMethod") String newAppMethod, @Param("appMethod") String appMethod);
   /**
    * 删除appkey —— method 关系
    * @param method
    * @return
    */
   int deleteAppMethod(@Param("method") String method);

   /**
    * 删除appkey —— method 关系
    * @param appKey
    * @return
    */
   int deleteAppKey(@Param("appKey") String appKey);

   List<String> findMethodByAppKey(@Param("appKey") String appKey);

}
