package com.jh.inf.service;

import com.github.pagehelper.PageInfo;
import com.jh.inf.bean.InfTask;
import com.jh.inf.bean.vo.InfTaskVo;

import java.util.List;
/**
 * 方法任务Service接口
 *
 * <AUTHOR>
 * @date 2022-08-10
 */
public interface InfTaskService {
	/**
	 *@Description: 保存或更新方法任务
	 *@param InfTask 方法任务对象
	 *@return String 方法任务ID
	 *@Author: qiubinbin
	 */
	String saveOrUpdateInfTask(InfTask InfTask);

	/**
	 *@Description: 删除方法任务
	 *@param ids void 方法任务ID
	 *@Author: qiubinbin
	 */
	void deleteInfTask(List<String> ids);

	/**
	 *@Description: 查询方法任务详情
	 *@param id
	 *@return InfTask
	 *@Author: qiubinbin
	 */
	InfTask findById(String id);

	/**
	 *@Description: 分页查询方法任务
	 *@param interfaceTaskVo
	 *@return PageInfo<InfTask>
	 *@Author: qiubinbin
	 */
	PageInfo<InfTask> findPageByQuery(InfTaskVo interfaceTaskVo);

	List<InfTask> getScriptByMethod(String method);
}
