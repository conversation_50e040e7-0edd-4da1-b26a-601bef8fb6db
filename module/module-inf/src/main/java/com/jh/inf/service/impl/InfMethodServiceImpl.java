package com.jh.inf.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.constant.Constant;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.inf.bean.InfMethod;
import com.jh.inf.bean.vo.InfMethodVo;
import com.jh.inf.dao.InfAppMapper;
import com.jh.inf.dao.InfMethodMapper;
import com.jh.inf.service.InfMethodService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.List;

//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;

/**
 * @Description: 应用令牌权限Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-01 14:06:26
 */
@Service
public class InfMethodServiceImpl extends BaseServiceImpl<InfMethodMapper, InfMethod>
		implements InfMethodService {

//	private static final Logger logger = LoggerFactory.getLogger(InfMethodServiceImpl.class);
	@Autowired
	private InfMethodMapper infMethodMapper;

	@Autowired
	private InfAppMapper infAppMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 * @Description: 保存或更新应用令牌权限
	 * @param infMethod 应用令牌权限对象
	 * @return String 应用令牌权限ID
	 * @Author: linqiang
	 */
	public String saveOrUpdate(InfMethod infMethod) {
		if (infMethod == null) {
			throw new ServiceException("数据异常");
		}
		if (StringUtils.isEmpty(infMethod.getId())) {
			// 新增
			infMethod.setId(UUIDUtils.getUUID());
			checkoutMethod(infMethod.getMethod());
			infMethodMapper.insertSelective(infMethod);
		} else {
			// 避免页面传入修改
			infMethod.setYn(null);
			InfMethod app = infMethodMapper.selectByPrimaryKey(infMethod.getId());
			if (!StringUtils.isEmpty(infMethod.getMethod())
					&& !app.getMethod().equals(infMethod.getMethod())) {
				checkoutMethod(infMethod.getMethod());
				infAppMapper.updateAppMethod(infMethod.getMethod(), app.getMethod());
			}
			infMethodMapper.updateByPrimaryKeySelective(infMethod);
		}
		return infMethod.getId();
	}

	/**
	 * 判断appKey是否重复
	 *
	 * @param method
	 */
	private void checkoutMethod(String method) {
		Example example = new Example(InfMethod.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("yn", Constant.YES);
		// 查询条件
		criteria.andEqualTo("method", method);
		int row = infMethodMapper.selectCountByExample(example);
		if (row > 0) {
			throw new ServiceException("method重复");
		}
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * @Description: 删除应用令牌权限
	 * @param id void 应用令牌权限ID
	 * @Author: linqiang
	 */
	public void delete(List<String> ids) {
		for (String id : ids) {
			InfMethod teminfMethod = new InfMethod();
			teminfMethod.setId(id);
			// TODO 做判断后方能执行删除
			InfMethod app = infMethodMapper.selectByPrimaryKey(id);
			if (app == null) {
				throw new ServiceException("非法请求");
			}
			infAppMapper.deleteAppMethod(app.getMethod());
			// 逻辑删除
			teminfMethod.setYn(Constant.NO);
			infMethodMapper.updateByPrimaryKeySelective(teminfMethod);
		}
	}

	/**
	 * @Description: 查询应用令牌权限详情
	 * @param id
	 * @return InfMethod
	 * @Author: linqiang
	 */
	@Override
	public InfMethod findById(String id) {
		return infMethodMapper.selectByPrimaryKey(id);
	}

	/**
	 * @Description: 分页查询应用令牌权限
	 * @param infMethodVo
	 * @return PageInfo<InfMethod>
	 * @Author: linqiang
	 */
	@Override
	public PageInfo<InfMethod> findPageByQuery(InfMethodVo infMethodVo) {
		PageHelper.startPage(infMethodVo.getPageNum(), infMethodVo.getPageSize());
		Example example = new Example(InfMethod.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("yn", Constant.YES);
		// 查询条件
		if (!StringUtils.isEmpty(infMethodVo.getMethod())) {
			criteria.andLike("method", "%" + infMethodVo.getMethod() + "%");
		}
		example.orderBy("createTime").desc();
		List<InfMethod> infMethodList = infMethodMapper.selectByExample(example);
		return new PageInfo<InfMethod>(infMethodList);
	}

	@Override
	public List<InfMethod> findAll() {
		Example example = new Example(InfMethod.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("yn", Constant.YES);
		return infMethodMapper.selectByExample(example);
	}
}
