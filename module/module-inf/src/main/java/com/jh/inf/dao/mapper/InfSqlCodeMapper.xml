<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.inf.dao.InfSqlCodeMapper" >
    <resultMap id="CommonBaseResultMap" type="com.jh.common.bean.BaseEntity">
    <result column="ID" property="id" jdbcType="VARCHAR" />
    <result column="YN" property="yn" jdbcType="INTEGER" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR" />
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
  </resultMap>
  <!-- API接口配置 -->
  <resultMap id="BaseResultMap" type="com.jh.inf.bean.InfSqlCode" extends="CommonBaseResultMap">
    <!--
      WARNING - @mbg.generated
    -->
    <!-- API调用说明 -->
    <result column="API" property="api" jdbcType="VARCHAR" />
    <!-- 接口标识 -->
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <!-- sql语句 -->
    <result column="SQL_CODE" property="sqlCode" jdbcType="VARCHAR" />
    <!-- 类型编码 -->
    <result column="TYPE_CODE" property="typeCode" jdbcType="VARCHAR" />
    <!-- sql说明 -->
    <result column="README" property="readme" jdbcType="VARCHAR" />
    <!-- 更新用户名称 -->
    <result column="UPDATE_USER_NICKNAME" property="updateUserNickname" jdbcType="VARCHAR" />
    <!-- 类型名称 -->
    <result column="TYPE_NAME" property="typeName" jdbcType="VARCHAR" />
  </resultMap>
</mapper>