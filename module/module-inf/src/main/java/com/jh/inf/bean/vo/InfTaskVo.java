package com.jh.inf.bean.vo;

import com.jh.inf.bean.InfTask;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;


/**
 *@Description: 方法任务Vo
 *@Author: linqiang
 *@Date: 2022-08-10
 */
@Schema(description = "InfTaskVo")
public class InfTaskVo extends InfTask {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	private List<InfTask> strs;
	private String method;

	public List<InfTask> getStrs() {
		return strs;
	}

	public void setStrs(List<InfTask> strs) {
		this.strs = strs;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	private Integer pageNum=1;
	private Integer pageSize=20;
	public Integer getPageNum() {
		return pageNum;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
}
