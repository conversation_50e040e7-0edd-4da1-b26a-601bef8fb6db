package com.jh.inf.controller;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.common.redis.RedisUtil;
import com.jh.constant.RedisConstant;
import com.jh.inf.bean.InfSqlCode;
import com.jh.inf.bean.vo.InfSqlCodeVo;
import com.jh.inf.service.InfSqlCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * API接口配置Controller
 * 
 * <AUTHOR>
 * @date 2022-08-01
 */
@RestController
@RequestMapping("/inf/code")
@Tag(name ="API接口配置")
public class InfSqlCodeController extends BaseController {
    @Autowired
    private InfSqlCodeService infSqlCodeService;
	@Autowired
	private RedisUtil redisUtil;
	private static final String PER_PREFIX = "btn:inf:code:";
	
	/**
	 *@Description: 新增API接口配置
	 *@param infSqlCode API接口配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	  @Operation(summary="新增API接口配置")
	@SystemLogAnnotation(type = "API接口配置",value = "新增API接口配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInfSqlCode(@RequestBody InfSqlCode infSqlCode) {
		String id = infSqlCodeService.saveOrUpdateInfSqlCode(infSqlCode);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改API接口配置
	 *@param infSqlCode API接口配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	  @Operation(summary="修改API接口配置")
	@SystemLogAnnotation(type = "API接口配置",value = "修改API接口配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInfSqlCode(@RequestBody InfSqlCode infSqlCode) {
		String id = infSqlCodeService.saveOrUpdateInfSqlCode(infSqlCode);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 批量删除API接口配置(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	  @Operation(summary="批量删除API接口配置")
	@SystemLogAnnotation(type = "API接口配置",value = "批量删除API接口配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInfSqlCode(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		infSqlCodeService.deleteInfSqlCode(ids);
		return RestApiResponse.ok();
	}
	/**
	 *@Description: 清除缓存
	 *@param code
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/clearCache/{code}")
	  @Operation(summary="清除缓存")
	@SystemLogAnnotation(type = "清除缓存",value = "清除缓存")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> clear(@PathVariable("code") String code){
		try {
			redisUtil.delMenu(RedisConstant.SYS_REDIS + "dp:" + code);
			logger.info("{}接口缓存清除成功",code);
		}catch (Exception e){
			logger.error("{}清除出错，{}",code,e);
			return RestApiResponse.error("缓存清除失败！");
		}
		return RestApiResponse.ok();
	}
	/**
	 *@Description: 查询API接口配置详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@GetMapping("/findById")
	  @Operation(summary="查询API接口配置详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InfSqlCode  infSqlCode=infSqlCodeService.findById(id);
		return RestApiResponse.ok(infSqlCode);
	}
	
	/**
	 *@Description: 分页查询API接口配置
	 *@param infSqlCodeVo API接口配置 查询条件
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@PostMapping("/findPageByQuery")
	  @Operation(summary="分页查询API接口配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InfSqlCodeVo infSqlCodeVo) {
		PageInfo<InfSqlCode>  infSqlCode=infSqlCodeService.findPageByQuery(infSqlCodeVo);
		return RestApiResponse.ok(infSqlCode);
	}
	
}
