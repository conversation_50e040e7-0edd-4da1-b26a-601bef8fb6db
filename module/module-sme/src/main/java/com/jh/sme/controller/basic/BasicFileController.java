package com.jh.sme.controller.basic;

import java.util.List;
import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.sme.bean.basic.BasicFile;
import com.jh.sme.bean.basic.vo.BasicFileVo;
import com.jh.sme.service.basic.BasicFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 文件管理附件Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/basic/file")
@Tag(name="文件管理附件")
public class BasicFileController extends BaseController{
    @Autowired
    private BasicFileService basicFileService;

	private static final String PER_PREFIX = "btn:basic:file:";
	
	/**
	 * 新增文件管理附件
	 *@param basicFile 文件管理附件数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@Operation(summary="新增文件管理附件")
	@SystemLogAnnotation(type = "文件管理附件",value = "新增文件管理附件")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveBasicFile(@RequestBody BasicFile basicFile) {
		String id = basicFileService.saveOrUpdateBasicFile(basicFile);
		return RestApiResponse.ok(id);
	}
	
	/**
	 * 修改文件管理附件
	 *@param basicFile 文件管理附件数据 json
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@Operation(summary="修改文件管理附件")
	@SystemLogAnnotation(type = "文件管理附件",value = "修改文件管理附件")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateBasicFile(@RequestBody BasicFile basicFile) {
		String id = basicFileService.saveOrUpdateBasicFile(basicFile);
		return RestApiResponse.ok(id);
	}
	
	/**
	 * 批量删除文件管理附件(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@Operation(summary="批量删除文件管理附件")
	@SystemLogAnnotation(type = "文件管理附件",value = "批量删除文件管理附件")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteBasicFile(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		basicFileService.deleteBasicFile(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 * 查询文件管理附件详情
	 *@param id
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@GetMapping("/findById")
	@Operation(summary="查询文件管理附件详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		BasicFile  basicFile=basicFileService.findById(id);
		return RestApiResponse.ok(basicFile);
	}
	
	/**
	 * 分页查询文件管理附件
	 *@param basicFileVo 文件管理附件 查询条件
	 *@return RestApiResponse<?>
	 *<AUTHOR>
	 */
	@PostMapping("/findPageByQuery")
	@Operation(summary="分页查询文件管理附件")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody BasicFileVo basicFileVo) {
		PageInfo<BasicFile>  basicFile=basicFileService.findPageByQuery(basicFileVo);
		return RestApiResponse.ok(basicFile);
	}

}
