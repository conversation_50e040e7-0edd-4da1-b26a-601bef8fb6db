package com.jh.sme.service.industry.impl;


import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.sme.bean.industry.IndustryList;
import com.jh.sme.bean.industry.IndustryListEnterprise;
import com.jh.sme.bean.industry.vo.IndustryListExcel;
import com.jh.sme.bean.industry.vo.IndustryListVo;
import com.jh.sme.dao.basic.BasicAreaMapper;
import com.jh.sme.dao.basic.BasicIndustryMapper;
import com.jh.sme.dao.industry.IndustryListMapper;
import com.jh.sme.dao.industry.IndustryListEnterpriseMapper;
import com.jh.sme.service.industry.IndustryListService;
import com.jh.sme.bean.basic.BasicFile;
import com.jh.sme.dao.basic.BasicFileMapper;
import com.jh.sys.dao.SysUserMapper;
import com.jh.sys.dao.SysRoleMapper;
import com.jh.common.bean.SysUser;
import com.jh.common.bean.SysRole;
import com.jh.sme.constant.RoleConstant;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;

import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import org.springframework.web.multipart.MultipartFile;

/**
 * 提升行业清单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
@Transactional(readOnly = true)
public class IndustryListServiceImpl extends BaseServiceImpl<IndustryListMapper, IndustryList> implements IndustryListService {

    private static final Logger logger = LoggerFactory.getLogger(IndustryListServiceImpl.class);
    @Autowired
    private IndustryListMapper industryListMapper;

    @Autowired
    private BasicFileMapper basicFileMapper;

    @Autowired
    private IndustryListEnterpriseMapper industryListEnterpriseMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private ImportService importService;

    @Autowired
    private BasicAreaMapper basicAreaMapper;

    @Autowired
    private BasicIndustryMapper basicIndustryMapper;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新提升行业清单
     *@param industryList 提升行业清单对象
     *@return String 提升行业清单ID
     *<AUTHOR>
     */
    public String saveOrUpdateIndustryList(IndustryList industryList) {
        if (industryList == null) {
            throw new ServiceException("数据异常");
        }
        
        // 判重逻辑：根据areaCode + majorCategory + minorCategory + supportYear进行判重
        Example example = new Example(IndustryList.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("areaCode", industryList.getAreaCode());
        criteria.andEqualTo("majorCategory", industryList.getMajorCategory());
        criteria.andEqualTo("minorCategory", industryList.getMinorCategory());
        criteria.andEqualTo("supportYear", industryList.getSupportYear());
        
        if (StringUtils.isEmpty(industryList.getId())) {
            // 新增时检查是否重复
            List<IndustryList> existingList = industryListMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(existingList)) {
                throw new ServiceException("行业已存在");
            }
            //新增
            industryList.setId(UUIDUtils.getUUID());
            industryListMapper.insertSelective(industryList);
        } else {
            // 更新时排除当前记录
            criteria.andNotEqualTo("id", industryList.getId());
            List<IndustryList> existingList = industryListMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(existingList)) {
                throw new ServiceException("行业已存在");
            }
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            industryList.setYn(null);
            industryListMapper.updateByPrimaryKeySelective(industryList);
        }
        return industryList.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除提升行业清单
     *@param ids void 提升行业清单ID
     *<AUTHOR>
     */
    public void deleteIndustryList(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            IndustryList industryList = industryListMapper.selectByPrimaryKey(id);
            if (industryList == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            IndustryList temindustryList = new IndustryList();
            temindustryList.setYn(CommonConstant.FLAG_NO);
            temindustryList.setId(industryList.getId());
            industryListMapper.updateByPrimaryKeySelective(temindustryList);
        }
    }

    /**
     * 查询提升行业清单详情
     *
     * @param id
     * @return IndustryList
     * <AUTHOR>
     */
    @Override
    public IndustryList findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return industryListMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询提升行业清单
     *
     * @param industryListVo
     * @return PageInfo<IndustryList>
     * <AUTHOR>
     */
    @Override
    public PageInfo<IndustryList> findPageByQuery(IndustryListVo industryListVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(industryListVo.getPageNum(), industryListVo.getPageSize());
        orderBy(industryListVo.getOrderColumn() + " " + industryListVo.getOrderValue());
        Example example = getExample(industryListVo);
        List<IndustryList> industryListList = industryListMapper.selectByExample(example);
        return new PageInfo<IndustryList>(industryListList);
    }

    private Example getExample(IndustryListVo vo) {
        Example example = new Example(IndustryList.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        // 检查用户角色权限
        checkUserRolePermission(criteria);

        if (!StringUtils.isEmpty(vo.getMinorCategory())) {
            criteria.andLike("minorCategory", "%" + vo.getMinorCategory() + "%");
        }

        // 行政区划代码查询
        if (!StringUtils.isEmpty(vo.getAreaCode())) {
            // 如果areaCode以00结尾，表示查询地级市下的所有数据，使用前4位进行模糊查询
            if (vo.getAreaCode().endsWith("00") && vo.getAreaCode().length() >= 6) {
                String cityCode = vo.getAreaCode().substring(0, 4);
                criteria.andLike("areaCode", cityCode + "%");
            } else {
                // 否则进行精确查询
                criteria.andEqualTo("areaCode", vo.getAreaCode());
            }
        }

        example.orderBy("createTime").desc();
        return example;
    }

    /**
     * 检查用户角色权限
     *
     * @param criteria 查询条件
     */
    private void checkUserRolePermission(Criteria criteria) {
        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();

        // 获取当前用户角色列表
        List<SysRole> roleList = sysRoleMapper.listByUserId(userId);

        // 判断用户角色
        boolean hasProvincialRole = false;
        boolean hasCityRole = false;
        boolean hasCountyRole = false;
        boolean hasOrgRole = false;

        // 检查用户角色
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_PROVINCIAL.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) {
                    hasProvincialRole = true;
                } else if (RoleConstant.ROLE_CITY.equals(roleCode)) {
                    hasCityRole = true;
                } else if (RoleConstant.ROLE_COUNTY.equals(roleCode)) {
                    hasCountyRole = true;
                } else if (RoleConstant.ROLE_ORG_ADMIN.equals(roleCode)) {
                    hasOrgRole = true;
                }
            }
        }

        // 机构角色不允许查看数据
        if (hasOrgRole && !hasProvincialRole && !hasCityRole && !hasCountyRole) {
            throw new ServiceException("机构角色无权限查看数据");
        }

        // 如果是省级角色，可以查看所有数据，不添加额外条件
        if (hasProvincialRole) {
            return;
        }

        // 获取用户信息
        SysUser user = new SysUser();
        user.setId(userId);
        SysUser currentUser = sysUserMapper.selectOne(user);

        if (currentUser != null && currentUser.getAreaCode() != null) {
            String areaCode = currentUser.getAreaCode();

            if (hasCityRole) {
                // 市级角色：获取用户的areaCode，去掉末尾的00，进行模糊匹配
                if (areaCode.endsWith("00")) {
                    areaCode = areaCode.substring(0, areaCode.length() - 2);
                }
                criteria.andLike("areaCode", areaCode + "%");
            } else if (hasCountyRole) {
                // 区县角色：精确匹配areaCode
                criteria.andEqualTo("areaCode", areaCode);
            }
        }
    }


    /**
     * 按条件导出查询提升行业清单
     *
     * @param industryListVo
     * @return PageInfo<IndustryList>
     * <AUTHOR>
     */
    @Override
    public List<IndustryList> findByQuery(IndustryListVo industryListVo) {
        orderBy(industryListVo.getOrderColumn() + " " + industryListVo.getOrderValue());
        Example example = getExample(industryListVo);
        return industryListMapper.selectByExample(example);
    }

    /**
     * 导入提升行业清单
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    @Override
    public void importIndustryListAsync(MultipartFile file, Integer cover) {
        //为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;
        importService.notification(appLoginUser, "已创建提升行业清单导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "提升行业清单", null);
        // 开始时间
        Date startTime = new Date();
        // 验证excel
        importService.verifyExcel(file);
        List<IndustryListExcel> list;
        // 读取数据
        try (InputStream inputStream = file.getInputStream()) {
            list = (List<IndustryListExcel>) EasyExcelUtils.readExcel(IndustryListExcel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容");
        }
        // 复制file 文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID() + "&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);
        //这里写数据处理逻辑
        CompletableFuture.runAsync(() -> {
            try {
                // 设置用户信息 避免异步处理数据时丢失用户信息
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(appLoginUser, null, appLoginUser.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                List<IndustryList> insertList = new ArrayList<>();
                //数据处理
                for (IndustryListExcel item : list) {
                    String errorMsg = "";
                    
                    try {
                        // 1. 数据读取和基本验证
                        String city = StringUtils.isEmpty(item.getCity()) ? "" : item.getCity().trim();
                        String county = StringUtils.isEmpty(item.getCounty()) ? "" : item.getCounty().trim();
                        String majorCategory = StringUtils.isEmpty(item.getMajorCategory()) ? "" : item.getMajorCategory().trim();
                        String minorCategory = StringUtils.isEmpty(item.getMinorCategory()) ? "" : item.getMinorCategory().trim();
                        String supportYear = StringUtils.isEmpty(item.getSupportYear()) ? "" : item.getSupportYear().trim();
                        
                        // 2. 字段验证
                        if (StringUtils.isEmpty(city)) {
                            errorMsg += minorCategory + "的市为空；";
                        }
                        if (StringUtils.isEmpty(county)) {
                            errorMsg += minorCategory + "的县(市、区)为空；";
                        }
                        if (StringUtils.isEmpty(majorCategory)) {
                            errorMsg += minorCategory + "的行业大类为空；";
                        }
                        if (StringUtils.isEmpty(minorCategory)) {
                            errorMsg += "行业小类为空；";
                        }
                        if (StringUtils.isEmpty(supportYear)) {
                            errorMsg += minorCategory + "的帮扶时间为空；";
                        }
                        
                        // 3. 通过城市和区县名称获取行政区划代码
                        String cityCode = getCityCodeByName(city);
                        String countyCode = "";
                        if (StringUtils.isEmpty(cityCode)) {
                            errorMsg += minorCategory + "的市不存在；";
                        } else {
                            countyCode = getCountyCodeByName(county, cityCode);
                            if (StringUtils.isEmpty(countyCode)) {
                                errorMsg += minorCategory + "的县(市、区)不存在；";
                            }
                        }
                        
                        // 5. 验证行业大类是否存在
                        if (!isValidMajorCategory(majorCategory)) {
                            errorMsg += minorCategory + "的行业大类不存在；";
                        }
                        
                        if (!StringUtils.isEmpty(errorMsg)) {
                            item.setImportSituation(ImportStatusEnum.ERROR.getName() + ":" + errorMsg);
                            continue;
                        }
                        
                        // 6. 判重处理
                        if (isDuplicateIndustryList(minorCategory, majorCategory, countyCode, supportYear)) {
                            item.setImportSituation(ImportStatusEnum.ERROR.getName() + ":" + minorCategory + "行业已存在");
                            continue;
                        }
                        
                        // 7. 创建实体对象
                        IndustryList industryList = new IndustryList();
                        industryList.setId(UUIDUtils.getUUID());
                        industryList.setMajorCategory(majorCategory);
                        industryList.setMinorCategory(minorCategory);
                        industryList.setCity(city);
                        industryList.setCounty(county);
                        industryList.setAreaCode(countyCode);
                        industryList.setSupportYear(supportYear);
                        industryList.setYn(CommonConstant.FLAG_YES);
                        industryList.setCreateUser(AppUserUtil.getCurrentUserName());
                        industryList.setCreateUserNickname(AppUserUtil.getCurrentRealName());
                        industryList.setCreateTime(new Date());
                        insertList.add(industryList);
                        item.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                        
                    } catch (Exception e) {
                        item.setImportSituation(ImportStatusEnum.ERROR.getName() + ":" + e.getMessage());
                        continue;
                    }
                }
                //保存数据
                if (!CollectionUtils.isEmpty(insertList)) {
                    //这里处理是否覆盖 TODO
                    industryListMapper.insertList(insertList);
                }
                String fileUrl = importService.faultDataAsync(list, IndustryListExcel.class, "导入结果.xls",
                        "提升行业清单", "提升行业清单", startTime, copyMultipartFile, appLoginUser);
                importService.notification(appLoginUser, "提升行业清单-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN, file.getOriginalFilename() + "导入成功", fileUrl);
            } catch (Exception e) {
                importService.notification(appLoginUser, e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "提升行业清单导入【出现异常】", null);
            } finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }

    /**
     * 保存提升行业清单（包含文件处理）
     *
     * @param vo 提升行业清单VO对象（包含文件列表）
     * @return String 提升行业清单ID
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public String saveIndustryListWithFiles(IndustryListVo vo) {
        // 验证参数
        if (vo == null) {
            throw new ServiceException("数据异常");
        }

        // 判断是新增还是更新
        boolean isUpdate = !StringUtils.isEmpty(vo.getId());
        IndustryList existingData = null;

        if (isUpdate) {
            // 更新现有数据
            existingData = industryListMapper.selectByPrimaryKey(vo.getId());
            if (existingData == null) {
                throw new ServiceException("要更新的数据不存在");
            }
            // TODO 做判断后方能执行修改 一般判断是否是自己的数据
        }

        IndustryList data;

        if (isUpdate) {
            // 更新现有数据
            data = existingData;
            // 更新主表字段
            BeanUtils.copyProperties(vo, data, "id", "createTime", "updateTime", "yn");

            // 更新主表
            industryListMapper.updateByPrimaryKeySelective(data);

            // 删除原有文件记录
            Example fileExample = new Example(BasicFile.class);
            Example.Criteria fileCriteria = fileExample.createCriteria();
            fileCriteria.andEqualTo("mainId", data.getId());
            fileCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
            List<BasicFile> existingFiles = basicFileMapper.selectByExample(fileExample);
            for (BasicFile file : existingFiles) {
                BasicFile deleteFile = new BasicFile();
                deleteFile.setId(file.getId());
                deleteFile.setYn(CommonConstant.FLAG_NO);
                basicFileMapper.updateByPrimaryKeySelective(deleteFile);
            }

            // 删除原有企业关联记录
            Example enterpriseExample = new Example(IndustryListEnterprise.class);
            Example.Criteria enterpriseCriteria = enterpriseExample.createCriteria();
            enterpriseCriteria.andEqualTo("listId", data.getId());
            enterpriseCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
            List<IndustryListEnterprise> existingEnterprises = industryListEnterpriseMapper.selectByExample(enterpriseExample);
            for (IndustryListEnterprise enterprise : existingEnterprises) {
                IndustryListEnterprise deleteEnterprise = new IndustryListEnterprise();
                deleteEnterprise.setId(enterprise.getId());
                deleteEnterprise.setYn(CommonConstant.FLAG_NO);
                industryListEnterpriseMapper.updateByPrimaryKeySelective(deleteEnterprise);
            }
        } else {
            // 新增数据
            data = new IndustryList();
            BeanUtils.copyProperties(vo, data);
            data.setId(UUIDUtils.getUUID());
            data.setYn(CommonConstant.FLAG_YES);

            // 插入主表数据
            industryListMapper.insertSelective(data);
        }

        // 保存文件信息
        if (vo.getFileList() != null && !vo.getFileList().isEmpty()) {
            int sortOrder = 1;
            for (IndustryListVo.FileInfo fileInfo : vo.getFileList()) {
                BasicFile basicFile = new BasicFile();
                basicFile.setId(UUIDUtils.getUUID());
                basicFile.setMainId(data.getId());
                basicFile.setFileName(fileInfo.getFileName());
                basicFile.setFileUrl(fileInfo.getFileUrl());
                basicFile.setFilePath(fileInfo.getFilePath());
                basicFile.setSortOrder(sortOrder++);
                basicFile.setYn(CommonConstant.FLAG_YES);
                basicFileMapper.insertSelective(basicFile);
            }
        }

        // 保存企业关联信息
        if (vo.getEnterpriseList() != null && !vo.getEnterpriseList().isEmpty()) {
            for (IndustryListVo.enterpriseList enterpriseItem : vo.getEnterpriseList()) {
                IndustryListEnterprise listEnterprise = new IndustryListEnterprise();
                listEnterprise.setId(UUIDUtils.getUUID());
                listEnterprise.setListId(data.getId());
                listEnterprise.setEnterpriseId(enterpriseItem.getEnterpriseId());
                listEnterprise.setYn(CommonConstant.FLAG_YES);
                industryListEnterpriseMapper.insertSelective(listEnterprise);
            }
        }

        return data.getId();
    }

    /**
     * 通过城市名称获取城市代码
     * @param cityName 城市名称
     * @return 城市代码
     */
    private String getCityCodeByName(String cityName) {
        if (StringUtils.isEmpty(cityName)) {
            return null;
        }
        try {
            return basicAreaMapper.getCityCodeByName(cityName);
        } catch (Exception e) {
            logger.error("查询城市代码失败: cityName={}", cityName, e);
            return null;
        }
    }

    /**
     * 通过区县名称和城市代码获取区县代码
     * @param countyName 区县名称
     * @param cityCode 城市代码
     * @return 区县代码
     */
    private String getCountyCodeByName(String countyName, String cityCode) {
        if (StringUtils.isEmpty(countyName) || StringUtils.isEmpty(cityCode)) {
            return null;
        }
        try {
            return basicAreaMapper.getCountyCodeByName(countyName, cityCode);
        } catch (Exception e) {
            logger.error("查询区县代码失败: countyName={}, cityCode={}", countyName, cityCode, e);
            return null;
        }
    }

    /**
     * 验证行业大类是否存在
     * @param majorCategory 行业大类名称
     * @return 是否存在
     */
    private boolean isValidMajorCategory(String majorCategory) {
        if (StringUtils.isEmpty(majorCategory)) {
            return false;
        }
        try {
            int count = basicIndustryMapper.countMajorCategoryByName(majorCategory);
            return count > 0;
        } catch (Exception e) {
            logger.error("验证行业大类失败: majorCategory={}", majorCategory, e);
            return false;
        }
    }

    /**
     * 判断行业清单是否重复
     * @param minorCategory 行业小类
     * @param majorCategory 行业大类
     * @param countyCode 区县代码
     * @param supportYear 帮扶年份
     * @return 是否重复
     */
    private boolean isDuplicateIndustryList(String minorCategory, String majorCategory, String countyCode, String supportYear) {
        if (StringUtils.isEmpty(minorCategory) || StringUtils.isEmpty(majorCategory) || 
            StringUtils.isEmpty(countyCode) || StringUtils.isEmpty(supportYear)) {
            return false;
        }
        
        Example example = new Example(IndustryList.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("minorCategory", minorCategory);
        criteria.andEqualTo("majorCategory", majorCategory);
        criteria.andEqualTo("areaCode", countyCode);
        criteria.andEqualTo("supportYear", supportYear);
        
        List<IndustryList> existingList = industryListMapper.selectByExample(example);
        return existingList != null && !existingList.isEmpty();
    }
}
