package com.jh.sme.bean.industry.vo;


import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * 提升行业清单对象 industry_list
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public class IndustryListExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "市", index = 0)
    private String city;
    @ExcelProperty(value = "区/县", index = 1)
    private String county;
    @ExcelProperty(value = "行业大类", index = 2)
    private String majorCategory;
    @ExcelProperty(value = "行业小类", index = 3)
    private String minorCategory;
    @ExcelProperty(value = "帮扶时间", index = 4)
    private String supportYear;
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 5)
    private String importSituation;

    public String getImportSituation() {
        return importSituation;
    }

    public void setImportSituation(String importSituation) {
        this.importSituation = importSituation;
    }

    /**
     * SET 市
     *
     * @param city
     */
    public void setCity(String city) {
        this.city = city == null ? null : city;
    }

    /**
     * GET 市
     *
     * @return city
     */
    public String getCity() {
        return city;
    }

    /**
     * SET 区/县
     *
     * @param county
     */
    public void setCounty(String county) {
        this.county = county == null ? null : county;
    }

    /**
     * GET 区/县
     *
     * @return county
     */
    public String getCounty() {
        return county;
    }

    /**
     * SET 行业大类
     *
     * @param majorCategory
     */
    public void setMajorCategory(String majorCategory) {
        this.majorCategory = majorCategory == null ? null : majorCategory;
    }

    /**
     * GET 行业大类
     *
     * @return majorCategory
     */
    public String getMajorCategory() {
        return majorCategory;
    }

    /**
     * SET 行业小类
     *
     * @param minorCategory
     */
    public void setMinorCategory(String minorCategory) {
        this.minorCategory = minorCategory == null ? null : minorCategory;
    }

    /**
     * GET 行业小类
     *
     * @return minorCategory
     */
    public String getMinorCategory() {
        return minorCategory;
    }

    /**
     * SET 帮扶时间
     *
     * @param supportYear
     */
    public void setSupportYear(String supportYear) {
        this.supportYear = supportYear == null ? null : supportYear;
    }

    /**
     * GET 帮扶时间
     *
     * @return supportYear
     */
    public String getSupportYear() {
        return supportYear;
    }
}
