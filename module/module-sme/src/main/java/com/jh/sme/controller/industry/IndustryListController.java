package com.jh.sme.controller.industry;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.sme.bean.industry.IndustryList;
import com.jh.sme.bean.industry.vo.IndustryListVo;
import com.jh.sme.service.industry.IndustryListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;


import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 提升行业清单Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/industry/list")
@Tag(name = "提升行业清单")
public class IndustryListController extends BaseController {
    @Autowired
    private IndustryListService industryListService;

    private static final String PER_PREFIX = "btn:industry:list:";

    /**
     * 新增提升行业清单
     *
     * @param industryList 提升行业清单数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增提升行业清单")
    @SystemLogAnnotation(type = "提升行业清单", value = "新增提升行业清单")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveIndustryList(@RequestBody IndustryList industryList) {
        String id = industryListService.saveOrUpdateIndustryList(industryList);
        return RestApiResponse.ok(id);
    }

    /**
     * 保存提升行业清单（包含文件处理）
     *
     * @param industryListVo 提升行业清单VO数据（包含文件列表）
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/saveWithFiles")
    @Operation(summary = "保存提升行业清单（包含文件）")
    @SystemLogAnnotation(type = "提升行业清单", value = "保存提升行业清单（包含文件）")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveIndustryListWithFiles(@RequestBody IndustryListVo industryListVo) {
        String id = industryListService.saveIndustryListWithFiles(industryListVo);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改提升行业清单
     *
     * @param industryList 提升行业清单数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改提升行业清单")
    @SystemLogAnnotation(type = "提升行业清单", value = "修改提升行业清单")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateIndustryList(@RequestBody IndustryList industryList) {
        String id = industryListService.saveOrUpdateIndustryList(industryList);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除提升行业清单(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除提升行业清单")
    @SystemLogAnnotation(type = "提升行业清单", value = "批量删除提升行业清单")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteIndustryList(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        industryListService.deleteIndustryList(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询提升行业清单详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询提升行业清单详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        IndustryList industryList = industryListService.findById(id);
        return RestApiResponse.ok(industryList);
    }

    /**
     * 分页查询提升行业清单
     *
     * @param industryListVo 提升行业清单 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询提升行业清单")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody IndustryListVo industryListVo) {
        PageInfo<IndustryList> industryList = industryListService.findPageByQuery(industryListVo);
        return RestApiResponse.ok(industryList);
    }

    @PostMapping("/excel")
    @Operation(summary = "导出提升行业清单")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "提升行业清单", sonMod = "提升行业清单", key = "jh_list_Table", isNotice = true, fileName = "提升行业清单")
    public RestApiResponse<?> excel(@RequestBody IndustryListVo industryListVo) {
        List<IndustryList> industryListList = industryListService.findByQuery(industryListVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(industryListList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入提升行业清单")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "cover") Integer cover) {
        industryListService.importIndustryListAsync(file, cover);
        return RestApiResponse.ok();
    }
}
