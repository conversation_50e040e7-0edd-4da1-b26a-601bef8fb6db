package com.jh.sme.service.industry;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.sme.bean.industry.IndustryEnterprise;
import com.jh.sme.bean.industry.vo.IndustryEnterpriseVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 企业导入Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IndustryEnterpriseService {
    /**
     * 保存或更新企业导入
     *
     * @param vo 企业导入对象
     * @return String 企业导入ID
     * <AUTHOR>
     */
    String saveOrUpdateIndustryEnterprise(IndustryEnterpriseVo vo);

    /**
     * 删除企业导入
     *
     * @param ids void 企业导入ID
     * <AUTHOR>
     */
    void deleteIndustryEnterprise(List<String> ids);

    /**
     * 查询企业导入详情
     *
     * @param id
     * @return IndustryEnterpriseVo
     * <AUTHOR>
     */
    IndustryEnterpriseVo findById(String id);

    /**
     * 分页查询企业导入
     *
     * @param industryEnterpriseVo
     * @return PageInfo<IndustryEnterprise>
     * <AUTHOR>
     */
    PageInfo<IndustryEnterprise> findPageByQuery(IndustryEnterpriseVo industryEnterpriseVo);

    /**
     * 按条件导出查询企业导入
     *
     * @param industryEnterpriseVo
     * @return PageInfo<IndustryEnterprise>
     * <AUTHOR>
     */
    List<IndustryEnterprise> findByQuery(IndustryEnterpriseVo industryEnterpriseVo);

    /**
     * 导入企业导入
     *
     * @param file
     * @return
     * <AUTHOR>
     */
    void importIndustryEnterpriseAsync(MultipartFile file);

    /**
     * 查询企业导入
     *
     * @param vo 企业导入 筛选条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    PageInfo<IndustryEnterprise> enterpriseList(IndustryEnterpriseVo vo);
}
