using FD.Framework.Data;
using FD.Model.Enum;
using FD.Model.SME;
using FD.Service.System;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using WebAPI.App_Code;
using WebAPI.Models.SME;
using Aspose.Cells;
using WebAPI.FilterHelpers;
using FD.Framework;

namespace WebAPI.Controllers
{
    /// <summary>
    /// 小微企业，企业
    /// </summary>
    public class SMEEnterpriseController : ApiController
    {

        protected LogHelper db = new LogHelper();

        #region 标准的增，删，改，查

        /// <summary>
        /// 取一条企业记录
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]

        public IHttpActionResult Enterprise(int id)
        {
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";

            try
            {
                var record = db.DbUtil.Get<SMEEnterprise>(id);
                if (record != null)
                {
                    result.code = 0;
                    result.data = record;
                    result.message = "操作成功";
                }
            }
            catch (Exception ex)
            {

                result.message = ex.ToString();
            }

            return Json(result);

        }




        /// <summary>
        /// 删除企业
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult Del(int id, int userid)
        {
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";

            try
            {
                var record = db.DbUtil.Get<SMEEnterprise>(id);
                record.IsDel = true;
                db.SaveLog<SMEEnterprise>(record, LogType.Edit, "小微企业，企业", "删除企业:" + record.Enterprise + "", userid);
                if (record != null)
                {
                    result.code = 0;
                    result.data = record;
                    result.message = "操作成功";
                }
            }
            catch (Exception ex)
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(new { id = id, userid = userid }));
                result.message = ex.ToString();
            }

            return Json(result);

        }



        /// <summary>
        /// 企业列表查询
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        //EnterpriseQuery query
        [ApiSMEAuthorize]
        public IHttpActionResult List(EnterpriseQuery query)
        //public IHttpActionResult List(dynamic requestval)
        {

            //dynamic requestval
            //EnterpriseQuery query = JsonConvert.DeserializeObject<EnterpriseQuery>(Convert.ToString(requestval));

            if (query.UserID <= 0)
                return null;

            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";
            try
            {

                query = SMEComm.QueryFilter(query) as EnterpriseQuery;

                string field = "e.*";
                string table = "SME_Enterprise e";
                string where = "where e.IsDel=0";
                string order = "e.id";

                if (!string.IsNullOrEmpty(query.Enterprise))
                {
                    where += " and e.Enterprise like  '%" + query.Enterprise + "%'";
                }
                if (!string.IsNullOrEmpty(query.USCC))
                {
                    where += " and e.USCC like  '%" + query.USCC + "%'";
                }

                if (!string.IsNullOrEmpty(query.CityCode))
                {
                    where += " and e.CityCode='" + query.CityCode + "'";
                }
                if (!string.IsNullOrEmpty(query.CountyCode))
                {
                    where += " and e.CountyCode='" + query.CountyCode + "'";
                }
                if (query.ImproveType > 0)
                {
                    where += " and e.ImproveType=" + query.ImproveType + "";
                }
                if (!string.IsNullOrEmpty(query.YearTime))
                {
                    where += " and e.YearTime='" + query.YearTime + "'";
                }
                if (!string.IsNullOrEmpty(query.OrgID))
                {
                    //where += " and oe.OrgID=" + query.OrgID + "";                   
                    where += " and o.HelperOrgCode='" + query.OrgID + "'";
                    table += " inner join SME_Org_Enterprise oe on e.id=oe.EnterpriseID";
                    table += " LEFT JOIN SME_HelperOrg o on o.id=oe.orgid ";

                }




                System.Data.DataSet ds = MsSqlUtils.GetDataSet(field, table, order, where, query.PageSize, query.PageIndex);
                if (ds != null && ds.Tables.Count == 2 && ds.Tables[1].Rows.Count == 1)
                {
                    result.code = 0;
                    result.message = "操作成功";
                    result.data = new { dataList = ds.Tables[0], count = ds.Tables[1].Rows[0][0] };
                }
            }
            catch (Exception ex)
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(query));
                result.message = ex.ToString();
            }

            return Json(result);

        }


        /// <summary>
        /// 保存一条企业信息
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="requestval"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]

        public IHttpActionResult Save(dynamic requestval)
        {
            WebAPI.Models.JsonResult jsonResult = new WebAPI.Models.JsonResult();
            try
            {

                EnterpriseParam param = JsonConvert.DeserializeObject<EnterpriseParam>(Convert.ToString(requestval));
                SMEEnterprise request = param.Model;
                int enterpriseId = request.Id; //企业ID,用于企业帮扶机构
                //2023-8-30获取当前年份
                //string year = DateTime.Now.Year.ToString();

                //检查是否重复
                SMEEnterprise model = null;
                if (request.Id == 0)//新增
                    model = db.DbUtil.GetT<SMEEnterprise>(p => p.CountyCode == request.CountyCode
                        && (p.Enterprise == request.Enterprise || p.USCC == request.USCC) && p.ImproveType == request.ImproveType && p.IsDel == false && p.YearTime == request.YearTime);
                else
                    model = db.DbUtil.GetT<SMEEnterprise>(p => p.CountyCode == request.CountyCode
                        && (p.Enterprise == request.Enterprise || p.USCC == request.USCC) && p.ImproveType == request.ImproveType && p.IsDel == false && p.Id != request.Id && p.YearTime == request.YearTime);
                if (model != null)
                {
                    jsonResult.code = -1;
                    jsonResult.message = "企业已存在";
                    return Json(jsonResult);
                }




                SMEEnterprise record;
                if (request.Id == 0)//新增
                {
                    record = new SMEEnterprise();
                }
                else
                {
                    record = db.DbUtil.Get<SMEEnterprise>(request.Id);
                }

                record.Enterprise = request.Enterprise;

                //[Enterprise], [USCC], [Address], [LinkMan], [LinkTel]
                record.USCC = request.USCC;
                record.Address = request.Address;
                record.LinkMan = request.LinkMan;
                record.LinkTel = request.LinkTel;
                record.ImproveType = request.ImproveType;

                record.CityCode = request.CityCode;
                record.CityName = request.CityName;
                record.CountyCode = request.CountyCode;
                record.CountyName = request.CountyName;
                //2023-8-21添加
                record.HelpState = request.HelpState;
                record.TerminateReason = request.TerminateReason;
                record.YearTime = request.YearTime;
                //存库操作
                if (request.Id == 0)//新增
                {
                    ///2023-8-21添加 帮扶年份字段

                    record.Inputtime = DateTime.Now;
                    record.IsDel = false;
                    record.UserID = request.UserID;
                    record.UserName = request.UserName;
                    // 用户名，
                    db.SaveLog<SMEEnterprise>(record, LogType.Add, "小微企业，企业", "新增企业:" + record.Enterprise + "", record.UserID);
                    var newRecord = db.DbUtil.GetT<SMEEnterprise>(p => p.IsDel == false && p.Inputtime == record.Inputtime && p.Enterprise == record.Enterprise && p.ImproveType == record.ImproveType);
                    if (newRecord != null) enterpriseId = newRecord.Id;

                }
                else
                {

                    db.SaveLog<SMEEnterprise>(record, LogType.Edit, "小微企业，企业", "修改企业:" + record.Enterprise + "", record.UserID);

                }

                // 帮扶关系
                UpdateEnterpriseOrg(enterpriseId, param.Ids, request.UserID);
                //attachmentModel.FildIds = param.DevelopmentFocus;
                //attachmentModel.TableName = "SME_Industry";
                //attachmentModel.BusinessType = "DevelopmentFocus";
                //SMEComm.UpdateAttachmentByType(attachmentModel);


                jsonResult.code = 0;
                jsonResult.data = record;
                jsonResult.message = "操作成功";
            }
            catch (Exception ex)
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(requestval));
                jsonResult.code = -5;
                jsonResult.message = ex.ToString();

            }


            return Json(jsonResult);

        }


        #endregion
        #region 相关接口 

        /// <summary>
        /// 根据企业取帮扶机构的列表
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IHttpActionResult OrgList(int id)
        {
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";

            try
            {

                string sql = @"SELECT o.ID,o.HelperOrgName
                                from SME_Org_Enterprise oe
                                left join SME_HelperOrg o on o.id=oe.OrgID
                                where EnterpriseID=" + id;

                System.Data.DataTable dt = MsSqlUtils.GetTable(sql);
                if (dt != null)
                {
                    result.code = 0;
                    result.data = dt;
                    result.message = "操作成功";
                }
            }
            catch (Exception ex)
            {

                result.message = ex.ToString();
            }

            return Json(result);
        }

        #endregion
        #region 辅助方法

        /// <summary>
        /// 更新企业机构关系
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="enterpriseId"></param>
        /// <param name="orgIds"></param>
        private void UpdateEnterpriseOrg(int enterpriseId, int[] orgIds, int userid)
        {
            string errInfo = "";

            try
            {
                //清企业机构
                string sql = "DELETE SME_Org_Enterprise where  EnterpriseID=" + enterpriseId;
                MsSqlUtils.GetDataSet(sql);

                if (orgIds == null || orgIds.Length == 0) return;
                //添企业机构
                foreach (var item in orgIds)
                {
                    SMEOrgEnterprise model = new SMEOrgEnterprise() { OrgID = item, EnterpriseID = enterpriseId };
                    db.SaveLog<SMEOrgEnterprise>(model, LogType.Add, "小微企业，企业帮扶机构", "新增企业帮扶机构:" + enterpriseId + "：" + item, userid);
                }
            }
            catch (Exception ex)
            {
                errInfo = ex.ToString();
            }
            finally
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, errInfo, JsonConvert.SerializeObject(new { enterpriseId = enterpriseId, orgIds = orgIds, userid = userid }));
            }
        }

        #endregion



        #region 导入导出



        /// <summary>
        /// /Manage/InstrumentApplyNew/ExportCheck.aspx 
        /// 导入按钮的操作,将导入上传的文件存成一个临时文件
        /// 创建：季李刚 2022-02-24
        /// </summary>
        [ApiSMEAuthorize]

        public IHttpActionResult Import(int improveType)
        {

            Models.JsonResult result = new Models.JsonResult();
            result.code = -1;
            string userId = HttpContext.Current.Request.QueryString["userid"];

            if (HttpContext.Current.Request.Files == null)
                result.message = "请勿上传空文件";

            if (HttpContext.Current.Request.Files.Count != 1)
                result.message = "只能上传一个文件";


            if (!string.IsNullOrEmpty(userId) && userId.ToIntEx() > 0)
                ;
            else
                result.message = "无权限";

            if (!string.IsNullOrEmpty(result.message))
                return Json(result);


            HttpPostedFile uploadFile = HttpContext.Current.Request.Files[0];
            string[] allowFileExt = { ".xls", ".xlsx" };

            var fileExt = Path.GetExtension(uploadFile.FileName);


            if (!allowFileExt.Contains(fileExt.ToLower()))
                result.message = "文件格式非法";

            if (uploadFile.ContentLength == 0)
                result.message = "请勿上传空文件";

            if (!string.IsNullOrEmpty(result.message))
                return Json(result);



            try
            {
                var path = "/UploadFile/SME/" + DateTime.Now.ToString("yyyyMM") + "/" + DateTime.Now.ToString("dd") + "/";
                var mapPath = HttpContext.Current.Server.MapPath(path); //硬盘物理目录
                var fileName = Guid.NewGuid().ToString();

                var mapPathFullPath = mapPath + fileName + fileExt; //硬盘物理路径                          
                var siteFullPath = path + fileName + fileExt; //网站路径


                if (!Directory.Exists(mapPath))
                    Directory.CreateDirectory(mapPath);
                uploadFile.SaveAs(mapPathFullPath);


                result = ImportData(mapPathFullPath);

            }
            catch (Exception)
            {
                result.message = "保存文件错误！";
            }

            return Json(result);
        }



        /// <summary>
        /// 导入操作，将临时文件中的数据倒入到库中
        ///  创建：季李刚 2022-02-24
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="sequenceNo"></param>
        private Models.JsonResult ImportData(string fileFullName)
        {
            //string fileFullName = Server.MapPath("/UploadFiles/Excel/" + fileName);
            Models.JsonResult result = new Models.JsonResult();
            result.code = -1;
            string userId = HttpContext.Current.Request.QueryString["userid"];
            string username = HttpContext.Current.Request.QueryString["username"];



            DataTable dt = null;
            List<SMEEnterprise> dataList = new List<SMEEnterprise>();//表中读到的企业
            List<SMEEnterprise> saveList = new List<SMEEnterprise>();//成功存入的企业
            List<string> msgList = new List<string>();


            try
            {
                dt = GetExcelTable(fileFullName, "Sheet1");
            }
            catch (Exception ex)
            {
                result.message = ex.ToString();
                //HttpRuntime.Cache[cacheKey] = "导入错误";
                return result;
            }

            string sql = @"SELECT code,name from Basic_City where provinceId='330000'";
            DataTable dtCity = MsSqlUtils.GetTable(sql);

            sql = "SELECT code,name,cityId from SME_Basic_Area ";
            DataTable dtCounty = MsSqlUtils.GetTable(sql);


            if (dt != null && dt.Columns.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    DataRow item = dt.Rows[i];
                    string msg = string.Empty;
                    SMEEnterprise model = new SMEEnterprise();
                    try
                    {
                        //1. 读取
                        //{ "", "", "", "", "", "", "", "" };
                        model.YearTime = item["帮扶时间"].ToString().Trim();
                        model.Enterprise = item["企业名称"].ToString().Trim();
                        model.USCC = item["统一社会信用代码"].ToString().Trim();
                        string improveType = item["企业类型"].ToString().Trim();
                        if (string.IsNullOrEmpty(improveType) != true && improveType.IndexOf("培育企业") != -1)
                            model.ImproveType = 2;
                        else if (string.IsNullOrEmpty(improveType) != true && improveType.IndexOf("体系导入") != -1)
                            model.ImproveType = 1;
                        //model.ImproveType = item[""].ToString();
                        model.Address = item["企业地址"].ToString().Trim();
                        model.LinkMan = item["联系人"].ToString().Trim();
                        model.LinkTel = item["联系方式"].ToString().Trim();
                        model.CityName = item["市"].ToString().Trim();
                        model.CountyName = item["县(市、区）"].ToString().Trim();
                        DataRow[] drCity = dtCity.Select("name='" + model.CityName + "'");
                        if (drCity != null && drCity.Count() == 1)
                            model.CityCode = drCity[0]["code"].ToString();
                        DataRow[] drCounty = dtCounty.Select("name='" + model.CountyName + "'"+ " and cityId='" + model.CityCode + "'");
                        if (drCounty != null && drCounty.Count() == 1)
                            model.CountyCode = drCounty[0]["code"].ToString();

                        if (string.IsNullOrEmpty(model.Enterprise))
                            continue;


                        // msg="企业名称为空";
                        if (string.IsNullOrEmpty(model.USCC))
                            msg += model.Enterprise + "的统一社会信用代码为空";
                        if (string.IsNullOrEmpty(model.Address))
                            msg += model.Enterprise + "企业地址的为空";
                        //if (string.IsNullOrEmpty(model.LinkMan))
                        //    msg += model.Enterprise + "联系人的为空";
                        //if (string.IsNullOrEmpty(model.LinkTel))
                        //    msg += model.Enterprise + "联系方式的为空";
                        if (string.IsNullOrEmpty(model.CityName))
                            msg += model.Enterprise + "的市为空";
                        if (string.IsNullOrEmpty(model.CountyName))
                            msg += model.Enterprise + "的县(市、区）为空";
                        if (model.ImproveType == 0)
                            msg += model.Enterprise + "的企业类型不合法";
                        if (string.IsNullOrEmpty(model.CityCode))
                            msg += model.Enterprise + "的市不存在";
                        if (string.IsNullOrEmpty(model.CountyCode))
                            msg += model.Enterprise + "的县(市、区）不存在";

                        if (!string.IsNullOrEmpty(msg))
                            msgList.Add(msg);
                        else
                        {
                            SMEComm.QueryFilter(model);//过滤非法字符
                            dataList.Add(model);
                        }

                    }
                    catch (Exception ex)
                    {
                        msgList.Add(ex.ToString());
                        continue;
                    }

                }

                if (msgList.Count > 0)
                {
                    result.message = "存在" + msgList.Count + "条数据错误,本次操作将不会存储任何数据。详情：";
                    result.message += string.Join("，", msgList);
                    return result;
                }

                //2.判重
                for (int i = 0; i < dataList.Count; i++)
                {
                    var model = dataList[i];
                    string msg = string.Empty;
                    var tempList = db.DbUtil.GetTList<SMEEnterprise>(p => p.IsDel == false && p.Enterprise == model.Enterprise && p.USCC == model.USCC && p.ImproveType == model.ImproveType && p.CountyCode == model.CountyCode);
                    if (tempList != null && tempList.Count > 0)//判重
                    {
                        msg += model.Enterprise + "已存在";
                        msgList.Add(msg);
                    }
                    else
                    {
                        saveList.Add(model);
                    }
                }


                //3.完善信息
                for (int i = 0; i < saveList.Count; i++)
                {
                    SMEEnterprise model = saveList[i];
                    model.Inputtime = DateTime.Now;
                    //model.YearTime = DateTime.Now.Year.ToString();
                    model.IsDel = false;
                    model.UserID = userId.ToIntEx();
                    model.UserName = username;
                }

                try
                {
                    //4. 并存表
                    db.SaveLog<SMEEnterprise>(saveList, LogType.Add, "小微企业，企业", username, null);
                }
                catch (Exception)
                {
                    result.code = -5;
                    result.message = "系统错误";
                    return result;
                }
            }

            if (string.IsNullOrEmpty(result.message))
            {
                result.code = 0;
                result.message = "成功导入" + saveList.Count + "条数据。";

                if (msgList.Count > 0)
                    result.message += "以下记录被忽略(" + string.Join(",", msgList) + ")";

            }

            return result;
            //HttpRuntime.Cache[cacheKey] = "导入完成";

        }



        /// <summary>
        /// 获取Excel中的数据，根据Sheet名字获取数据
        /// </summary>
        /// <param Name="excelPath"></param>
        /// <param Name="sheetName"></param>
        /// <returns></returns>
        public static DataTable GetExcelTable(string excelPath, string sheetName)
        {
            if (!File.Exists(excelPath))
                throw new Exception("解析文件失败");

            DataTable dt = new DataTable();

            Workbook workbook = new Workbook(excelPath);
            Cells cells = workbook.Worksheets[sheetName].Cells;
            if (cells == null)
                throw new Exception("请将数据存于工作表：Sheet1");

            for (int i = 0; i < cells.MaxDataRow + 1; i++)
            {
                if (i == 0)
                {
                    for (int j = 0; j < cells.MaxDataColumn + 1; j++)
                    {
                        dt.Columns.Add(cells[i, j].StringValue.Trim());
                    }
                }
                else
                {
                    DataRow dr = dt.NewRow();
                    for (int j = 0; j < cells.MaxDataColumn + 1; j++)
                    {
                        dr[j] = cells[i, j].StringValue.Trim();
                    }
                    dt.Rows.Add(dr);
                }
            }

            //		行业大类	行业小类						
            string[] titles = { "市", "县(市、区）", "企业类型", "企业名称", "统一社会信用代码", "企业地址", "联系人", "联系方式", "帮扶时间" };
            //string[] titles = { "企业名称", "统一社会信用代码", "企业地址", "联系人", "联系方式", "企业类型", "帮扶机构", "帮扶阶段", "终止帮扶理由", "所属行业" };
            foreach (string title in titles)
            {
                if (!dt.Columns.Contains(title))
                {
                    throw new Exception("模板无法解析");
                }
            }


            return dt;
        }



        //导出前,先将导出数据都写入临时文件，并将临时文件的物理路径返回给前台（继续调用  Export）
        // public IHttpActionResult PreExport(EnterpriseQuery query)
        [ApiSMEAuthorize]

        public IHttpActionResult PreExport(dynamic requestval)
        {

            EnterpriseQuery query = JsonConvert.DeserializeObject<EnterpriseQuery>(Convert.ToString(requestval));
            //EnterpriseQuery query = new EnterpriseQuery();
            Models.JsonResult result = new Models.JsonResult();
            string msg = string.Empty;

            DataTable dt = ExportData(query);//1.取得要导出的数据
            string filepath = string.Empty;
            if (dt != null && dt.Rows.Count > 0)
            {
                if (query.ImproveType == 1)
                {
                    filepath = GetExportFile();//2.将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
                }
                else if (query.ImproveType == 2)
                {
                    filepath = GetExportFile();  // GetNurtureExportFile();//2.将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
                }
            }

            if (dt != null && dt.Rows.Count > 0 && !string.IsNullOrEmpty(filepath))
            {
                try
                {
                    WriteExportFile(dt, filepath, query.ImproveType);//3.将数据写入临时文件
                }
                catch (Exception ex)
                {
                    msg = "操作文件时发生错误";
                    ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(requestval));
                }
            }
            else if (dt == null || dt.Rows.Count == 0)
                msg = "无有效数据可以导出";
            else if (string.IsNullOrEmpty(filepath))
                msg = "文件系统错误";

            //4.输出结果
            if (!string.IsNullOrEmpty(msg))//有异常
            {
                result.code = -1;
                result.message = msg;
            }
            else
            {
                var dicPath = filepath;
                filepath = filepath.Replace(HttpContext.Current.Server.MapPath("/"), "").Replace("\\", "/");
                filepath = HttpUtility.UrlEncode("/" + filepath);
                result.code = 0;
                result.message = "操作成功";
                result.data = filepath;
            }


            return Json(result);
        }


        /// <summary>
        /// 导出
        /// 创建：季李刚 2022-03-11
        /// </summary>
        /// <returns></returns>

        //public System.Net.Http.HttpResponseMessage Export(EnterpriseQuery query)
        public System.Net.Http.HttpResponseMessage Export(string path)
        {
            //string path = WirteExcel(query);//将数据写入的一个临时文件中
            //path= HttpUtility.UrlDecode(path);
            //path = HttpContext.Current.Server.MapPath(path);
            //if(!File.Exists(path))
            //    return new System.Net.Http.HttpResponseMessage(System.Net.HttpStatusCode.NoContent);

            //try
            //{
            //    var stream = new FileStream(path, FileMode.Open);
            //    System.Net.Http.HttpResponseMessage response = new System.Net.Http.HttpResponseMessage(System.Net.HttpStatusCode.OK);
            //    response.Content = new System.Net.Http.StreamContent(stream);
            //    response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");
            //    string downloadFileName = System.Web.HttpUtility.UrlEncode("企业信息.xls", System.Text.Encoding.UTF8);//解决文件名乱码
            //    response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
            //    {
            //        FileName = downloadFileName
            //    };

            //    Action<string> action = new Action<string>(DeleteFile);//文件导出后，删除临时文件
            //    action.BeginInvoke(path, null, null);
            //    return response;
            //}
            //catch
            //{
            //    return new System.Net.Http.HttpResponseMessage(System.Net.HttpStatusCode.NoContent);
            //}

            return SMEComm.Export(path, "企业信息.xls");
        }




        ///// <summary>
        ///// 文件导出后，删除临时文件
        ///// 创建：季李刚 2022-03-11
        ///// </summary>
        ///// <param name="filePath"></param>
        //private void DeleteFile(string filePath)
        //{
        //    System.Threading.Thread.Sleep(2000);
        //    try
        //    {
        //        if (File.Exists(filePath))
        //            File.Delete(filePath);
        //    }
        //    catch { }

        //}

        /// <summary>
        /// 取得导出数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        private DataTable ExportData(EnterpriseQuery query)
        {
            DataTable dt = null;
            query = SMEComm.QueryFilter(query) as EnterpriseQuery;

            //string field = "*,ROW_NUMBER() OVER(ORDER BY id) rownumber";
            //string table = "SME_Enterprise";

            string field = "a.*,ROW_NUMBER() OVER(ORDER BY a.id) rownumber,stuff(( select ','+ HelperOrgName from (SELECT HelperOrgName from SME_HelperOrg b where b.ID in(select c.OrgID from SME_Org_Enterprise c where c.EnterpriseID=a.ID)) temp for xml path('')),1,1,'') as orgName,stuff(( select ','+ Name from (SELECT Name from SME_Industry b where b.ID in(select c.IndustryID from SME_Industry_Enterprise c where c.EnterpriseID=a.ID)) temp for xml path('')),1,1,'') as IndustryName";
            string table = "SME_Enterprise a";

            string where = "where IsDel=0";
            string order = "id";

            if (!string.IsNullOrEmpty(query.Enterprise))
            {
                where += " and Enterprise like  '%" + query.Enterprise + "%'";
            }
            if (!string.IsNullOrEmpty(query.USCC))
            {
                where += " and USCC like  '%" + query.USCC + "%'";
            }

            if (!string.IsNullOrEmpty(query.CityCode))
            {
                where += " and CityCode='" + query.CityCode + "'";
            }
            if (!string.IsNullOrEmpty(query.CountyCode))
            {
                where += " and CountyCode='" + query.CountyCode + "'";
            }
            if (query.ImproveType > 0)
            {
                where += " and ImproveType=" + query.ImproveType + "";
            }
            if (!string.IsNullOrEmpty(query.YearTime))
            {
                where += " and YearTime='" + query.YearTime + "'";
            }

            dt = MsSqlUtils.CommonQuery(field, table, order, where);
            return dt;
        }


        /// <summary>
        /// 导出，将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
        /// </summary>
        /// <returns>返回临时文件的物理路径</returns>
        private string GetExportFile()
        {
            //string tempPath = TEMPATE_PATH;//模板路径 
            string tempPath = "/UploadFile/Template/企业模板.xls";//模板路径 

            return SMEComm.GetExportFile(tempPath);

        }

        /// <summary>
        /// 导出，将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
        /// </summary>
        /// <returns>返回临时文件的物理路径</returns>
        private string GetNurtureExportFile()
        {
            //string tempPath = TEMPATE_PATH;//模板路径 
            string tempPath = "/UploadFile/Template/培育类企业模板.xls";//模板路径 

            return SMEComm.GetExportFile(tempPath);

        }

        /// <summary>
        /// 将数据写入导出模板
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="filepath"></param>
        private void WriteExportFile(DataTable dt, string filepath, int type)
        {
            AsposeLicenseHelper asposeLicenseHelper = new AsposeLicenseHelper();
            asposeLicenseHelper.LicenseCell();//Aspose 授权

            Workbook workbook = new Workbook(filepath);
            Worksheet sheet1 = workbook.Worksheets[0];
            Cells cells = sheet1.Cells;

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                DataRow dr = dt.Rows[i];
                int rowNumber = i + 2;
                cells["A" + rowNumber].PutValue(dr["rownumber"].ToString());
                cells["B" + rowNumber].PutValue(dr["CityName"].ToString());
                cells["C" + rowNumber].PutValue(dr["CountyName"].ToString());
                cells["D" + rowNumber].PutValue(dr["Enterprise"].ToString());
                cells["E" + rowNumber].PutValue(dr["USCC"].ToString());
                cells["F" + rowNumber].PutValue(dr["Address"].ToString());
                cells["G" + rowNumber].PutValue(dr["LinkMan"].ToString());
                cells["H" + rowNumber].PutValue(dr["LinkTel"].ToString());
                string ImproveType = dr["ImproveType"].ToString();
                if (ImproveType == "1")
                    ImproveType = "体系导入类企业";
                else if (ImproveType == "2")
                    ImproveType = "培育类企业";
                else
                    ImproveType = "";
                cells["I" + rowNumber].PutValue(ImproveType);
                cells["J" + rowNumber].PutValue(dr["orgName"].ToString());
                cells["K" + rowNumber].PutValue(dr["HelpState"].ToString());
                cells["L" + rowNumber].PutValue(dr["TerminateReason"].ToString());
                cells["M" + rowNumber].PutValue(dr["IndustryName"].ToString());
                cells["N" + rowNumber].PutValue(dr["YearTime"].ToString());
                //if (type == 1)
                //{
                //    cells["K" + rowNumber].PutValue(dr["HelpState"].ToString());
                //    cells["L" + rowNumber].PutValue(dr["TerminateReason"].ToString());
                //    cells["M" + rowNumber].PutValue(dr["IndustryName"].ToString());
                //    cells["N" + rowNumber].PutValue(dr["YearTime"].ToString());
                //}
                //else
                //{
                //    cells["K" + rowNumber].PutValue(dr["IndustryName"].ToString());
                //}

            }

            workbook.Save(filepath);
        }





        #endregion


    }
}