package com.jh.sme.service.basic;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.jh.sme.bean.basic.BasicIndustry;
import com.jh.sme.bean.basic.vo.BasicIndustryVo;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
/**
 * 行业Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface BasicIndustryService{
	/**
	 * 保存或更新行业
	 *@param basicIndustry 行业对象
	 *@return String 行业ID
	 *<AUTHOR>
	 */
	String saveOrUpdateBasicIndustry(BasicIndustry basicIndustry);
	
	/**
	 * 删除行业
	 *@param ids void 行业ID
	 *<AUTHOR>
	 */
	void deleteBasicIndustry(List<String> ids);

	/**
	 * 查询行业详情
	 *@param id
	 *@return BasicIndustry
	 *<AUTHOR>
	 */
	BasicIndustry findById(String id);

	/**
	 * 分页查询行业
	 *@param basicIndustryVo
	 *@return PageInfo<BasicIndustry>
	 *<AUTHOR>
	 */
	PageInfo<BasicIndustry> findPageByQuery(BasicIndustryVo basicIndustryVo);

	/**
	 * 查询行业大类列表（yn=1且PARENT_CODE=0）
	 *@return List<BasicIndustry>
	 *<AUTHOR>
	 */
	List<BasicIndustry> findMajorCategories();

}
