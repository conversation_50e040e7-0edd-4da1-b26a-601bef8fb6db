package com.jh.sme.service.word;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.sme.bean.word.WorkDocument;
import com.jh.sme.bean.word.vo.WorkDocumentVo;

/**
 * 政策文件Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface WorkDocumentService {
    /**
     * 保存或更新政策文件
     *
     * @param workDocumentVo 政策文件VO对象
     * @return String 政策文件ID
     * <AUTHOR>
     */
    String saveOrUpdateWorkDocument(WorkDocumentVo workDocumentVo);

    /**
     * 删除政策文件
     *
     * @param ids void 政策文件ID
     * <AUTHOR>
     */
    void deleteWorkDocument(List<String> ids);

    /**
     * 查询政策文件详情
     *
     * @param id
     * @return WorkDocument
     * <AUTHOR>
     */
    WorkDocumentVo findById(String id);

    /**
     * 分页查询政策文件
     *
     * @param workDocumentVo
     * @return PageInfo<WorkDocument>
     * <AUTHOR>
     */
    PageInfo<WorkDocument> findPageByQuery(WorkDocumentVo workDocumentVo);

    /**
     * 查询所有政策文件列表
     *
     * @return List<WorkDocument>
     */
    List<WorkDocument> findList();

}