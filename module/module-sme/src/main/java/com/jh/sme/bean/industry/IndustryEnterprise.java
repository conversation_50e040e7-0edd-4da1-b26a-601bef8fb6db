package com.jh.sme.bean.industry;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 企业导入对象 industry_enterprise
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Table(name = "industry_enterprise")
@Schema(description = "企业导入")
@Data
public class IndustryEnterprise extends BaseEntity {
    private static final long serialVersionUID = 1L;


    @Column(name = "ENTERPRISE_TYPE")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "企业类型(1体系导入企业 2培育类企业)")
    private Integer enterpriseType;
    @Column(name = "ENTERPRISE_TYPE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业类型")
    private String enterpriseTypeName;
    @Column(name = "USCC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "统一社会信用代码")
    private String uscc;
    @Column(name = "ENTERPRISE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业名称")
    private String enterpriseName;
    @Column(name = "AREA_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划代码")
    private String areaCode;
    @Column(name = "AREA_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划名称")
    private String areaName;
    @Column(name = "CITY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "市")
    private String city;
    @Column(name = "COUNTY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "县/区")
    private String county;
    @Column(name = "ADDRESS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "地址")
    private String address;
    @Column(name = "CONTACT_PERSON")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "联系人")
    private String contactPerson;
    @Column(name = "CONTACT_PHONE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "联系方式")
    private String contactPhone;
    @Column(name = "HELP_YEAR")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "帮扶时间（年份）")
    private String helpYear;
    @Column(name = "HELP_STATUS")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "帮扶状态（1已完成帮扶 2持续帮扶 3终止帮扶）")
    private Integer helpStatus;

}
