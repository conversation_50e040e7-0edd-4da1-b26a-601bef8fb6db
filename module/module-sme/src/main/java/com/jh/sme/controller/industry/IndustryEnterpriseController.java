package com.jh.sme.controller.industry;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.sme.bean.industry.IndustryEnterprise;
import com.jh.sme.bean.industry.vo.IndustryEnterpriseVo;
import com.jh.sme.service.industry.IndustryEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;

import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 企业导入Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/industry/enterprise")
@Tag(name = "企业导入")
public class IndustryEnterpriseController extends BaseController {
    @Autowired
    private IndustryEnterpriseService industryEnterpriseService;

    private static final String PER_PREFIX = "btn:industry:enterprise:";

    /**
     * 新增企业导入
     *
     * @param vo 企业导入数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增企业导入")
    @SystemLogAnnotation(type = "企业导入", value = "新增企业导入")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveIndustryEnterprise(@RequestBody IndustryEnterpriseVo vo) {
        String id = industryEnterpriseService.saveOrUpdateIndustryEnterprise(vo);
        return RestApiResponse.ok(id);
    }


    /**
     * 批量删除企业导入(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除企业导入")
    @SystemLogAnnotation(type = "企业导入", value = "批量删除企业导入")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteIndustryEnterprise(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        industryEnterpriseService.deleteIndustryEnterprise(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询企业导入详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询企业导入详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        IndustryEnterpriseVo industryEnterpriseVo = industryEnterpriseService.findById(id);
        return RestApiResponse.ok(industryEnterpriseVo);
    }

    /**
     * 分页查询企业导入
     *
     * @param industryEnterpriseVo 企业导入 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询企业导入")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody IndustryEnterpriseVo industryEnterpriseVo) {
        PageInfo<IndustryEnterprise> industryEnterprise = industryEnterpriseService.findPageByQuery(industryEnterpriseVo);
        return RestApiResponse.ok(industryEnterprise);
    }

    /**
     * 查询企业导入
     *
     * @param vo 企业导入 筛选条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/enterpriseList")
    @Operation(summary = "分页查询企业导入")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> enterpriseList(@RequestBody IndustryEnterpriseVo vo) {
        PageInfo<IndustryEnterprise> industryEnterprise = industryEnterpriseService.enterpriseList(vo);
        return RestApiResponse.ok(industryEnterprise);
    }


    @PostMapping("/excel")
    @Operation(summary = "导出企业导入")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "excel')")
    // 这里的模块名称和子模块名称，请根据实际情况填写
    @ExportRespAnnotation(modType = "企业导入", sonMod = "企业导入", key = "jh_enterprise_Table", isNotice = true, fileName = "企业导入")
    public RestApiResponse<?> excel(@RequestBody IndustryEnterpriseVo industryEnterpriseVo) {
        List<IndustryEnterprise> industryEnterpriseList = industryEnterpriseService.findByQuery(industryEnterpriseVo);
        //这里对数据进行转换处理，如没使用字典的或关联其它表的数据。如在查询中已处理请忽略
        return RestApiResponse.ok(industryEnterpriseList);
    }

    @PostMapping("/import")
    @Operation(summary = "导入企业导入")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "import')")
    public RestApiResponse<?> importTX(@RequestParam("file") MultipartFile file) {
        industryEnterpriseService.importIndustryEnterpriseAsync(file);
        return RestApiResponse.ok();
    }

}
