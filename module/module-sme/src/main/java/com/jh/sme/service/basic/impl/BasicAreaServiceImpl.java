package com.jh.sme.service.basic.impl;

import com.jh.sme.bean.basic.BasicArea;
import com.jh.sme.bean.basic.vo.BasicAreaVo;
import com.jh.sme.bean.basic.vo.MarketSupervisionVo;
import com.jh.sme.dao.basic.BasicAreaMapper;
import com.jh.sme.service.basic.BasicAreaService;

import com.jh.common.bean.SysUser;
import com.jh.sys.service.SysUserService;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import com.jh.utils.ImportService;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 行业基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
@Transactional(readOnly = true)
public class BasicAreaServiceImpl extends BaseServiceImpl<BasicAreaMapper, BasicArea> implements BasicAreaService {

    private static final Logger logger = LoggerFactory.getLogger(BasicAreaServiceImpl.class);
    @Autowired
    private BasicAreaMapper basicAreaMapper;

    @Autowired
    private ImportService importService;

    @Autowired
    private SysUserService sysUserService;


    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新行业基础信息
     *@param basicArea 行业基础信息对象
     *@return String 行业基础信息ID
     *<AUTHOR>
     */
    public String saveOrUpdateBasicArea(BasicArea basicArea) {
        if (basicArea == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(basicArea.getId())) {
            //新增
            basicArea.setId(UUIDUtils.getUUID());
            basicAreaMapper.insertSelective(basicArea);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            basicArea.setYn(null);
            basicAreaMapper.updateByPrimaryKeySelective(basicArea);
        }
        return basicArea.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除行业基础信息
     *@param ids void 行业基础信息ID
     *<AUTHOR>
     */
    public void deleteBasicArea(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            BasicArea basicArea = basicAreaMapper.selectByPrimaryKey(id);
            if (basicArea == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            BasicArea temBasicArea = new BasicArea();
            temBasicArea.setYn(CommonConstant.FLAG_NO);
            temBasicArea.setId(basicArea.getId());
            basicAreaMapper.updateByPrimaryKeySelective(temBasicArea);
        }
    }

    /**
     * 查询行业基础信息详情
     *
     * @param id
     * @return BasicArea
     * <AUTHOR>
     */
    @Override
    public BasicArea findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return basicAreaMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询行业基础信息
     *
     * @param basicAreaVo
     * @return PageInfo<BasicArea>
     * <AUTHOR>
     */
    @Override
    public PageInfo<BasicArea> findPageByQuery(BasicAreaVo basicAreaVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(basicAreaVo.getPageNum(), basicAreaVo.getPageSize());
        orderBy(basicAreaVo.getOrderColumn() + " " + basicAreaVo.getOrderValue());
        Example example = getExample(basicAreaVo);
        List<BasicArea> basicAreaList = basicAreaMapper.selectByExample(example);
        return new PageInfo<BasicArea>(basicAreaList);
    }

    private Example getExample(BasicAreaVo basicAreaVo) {
        Example example = new Example(BasicArea.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        if (!StringUtils.isEmpty(basicAreaVo.getName())) {
            criteria.andLike("name", "%" + basicAreaVo.getName() + "%");
        }
        if (!StringUtils.isEmpty(basicAreaVo.getCode())) {
            criteria.andLike("code", basicAreaVo.getCode() + "%");
        }
        if (basicAreaVo.getLevel() != null) {
            criteria.andEqualTo("level", basicAreaVo.getLevel());
        }
        if (Boolean.TRUE.equals(basicAreaVo.getOnlyProvince())) {
            criteria.andEqualTo("level", 0L);
        }
        if (Boolean.TRUE.equals(basicAreaVo.getOnlyCity())) {
            criteria.andEqualTo("level", 1L);
        }
        if (Boolean.TRUE.equals(basicAreaVo.getOnlyCounty())) {
            criteria.andEqualTo("level", 2L);
        }
        if (!StringUtils.isEmpty(basicAreaVo.getParentCode())) {
            criteria.andLike("code", basicAreaVo.getParentCode() + "%");
            criteria.andNotEqualTo("code", basicAreaVo.getParentCode());
        }
        example.orderBy("sort").asc();
        return example;
    }

    /**
     * 查询省级数据
     *
     * @return List<BasicArea>
     */
    @Override
    public List<BasicArea> findProvinceList() {
        return basicAreaMapper.findProvinceList();
    }

    /**
     * 查询市级数据
     *
     * @return List<BasicArea>
     */
    @Override
    public List<BasicArea> findCityList() {
        return basicAreaMapper.findCityList();
    }

    /**
     * 查询区县级数据
     *
     * @return List<BasicArea>
     */
    @Override
    public List<BasicArea> findCountyList() {
        return basicAreaMapper.findCountyList();
    }

    /**
     * 根据父级代码查询下级区划
     *
     * @param parentCode 父级代码
     * @return List<BasicArea>
     */
    @Override
    public List<BasicArea> findByParentCode(String parentCode) {
        if (StringUtils.isEmpty(parentCode)) {
            return new ArrayList<>();
        }
        return basicAreaMapper.findByParentCode(parentCode);
    }

    /**
     * 根据级别查询区划
     *
     * @param level 级别
     * @return List<BasicArea>
     */
    @Override
    public List<BasicArea> findByLevel(Long level) {
        if (level == null) {
            return new ArrayList<>();
        }
        return basicAreaMapper.findByLevel(level);
    }

    /**
     * 获取省市区县树形结构
     *
     * @return List<BasicArea>
     */
    @Override
    public List<BasicArea> getAreaTree() {
        // 获取所有区划
        Example example = new Example(BasicArea.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        example.orderBy("sort").asc();
        List<BasicArea> allAreas = basicAreaMapper.selectByExample(example);

        // 按级别分组
        Map<Long, List<BasicArea>> levelMap = allAreas.stream()
                .collect(Collectors.groupingBy(BasicArea::getLevel));

        // 构建树形结构
        List<BasicArea> provinceList = levelMap.getOrDefault(0L, new ArrayList<>());
        List<BasicArea> cityList = levelMap.getOrDefault(1L, new ArrayList<>());
        List<BasicArea> countyList = levelMap.getOrDefault(2L, new ArrayList<>());

        // 构建省市区县关系
        Map<String, List<BasicArea>> cityMap = new HashMap<>();
        Map<String, List<BasicArea>> countyMap = new HashMap<>();

        // 市级按省级分组
        for (BasicArea city : cityList) {
            String provinceCode = city.getCode().substring(0, 2) + "0000";
            cityMap.computeIfAbsent(provinceCode, k -> new ArrayList<>()).add(city);
        }

        // 区县级按市级分组
        for (BasicArea county : countyList) {
            String cityCode = county.getCode().substring(0, 4) + "00";
            countyMap.computeIfAbsent(cityCode, k -> new ArrayList<>()).add(county);
        }

        // 构建省-市-区县的树形结构
        for (BasicArea province : provinceList) {
            // 设置省级的子节点（市级）
            List<BasicArea> cities = cityMap.getOrDefault(province.getCode(), new ArrayList<>());
            province.setChildren(cities);

            // 设置市级的子节点（区县级）
            for (BasicArea city : cities) {
                List<BasicArea> counties = countyMap.getOrDefault(city.getCode(), new ArrayList<>());
                city.setChildren(counties);
            }
        }

        return provinceList;
    }

    /**
     * 根据用户ID获取用户下属的行政区划列表（包含本级和下属的层级结构）
     *
     * @param userId 用户ID
     * @return List<BasicArea>
     */
    @Override
    public List<BasicArea> getUserSubordinateAreas(String userId) {
        try {
            // 获取用户信息
            SysUser user = sysUserService.findById(userId);
            if (user == null) {
                logger.warn("用户不存在，userId: {}", userId);
                return new ArrayList<>();
            }

            String areaCode = user.getAreaCode();

            // 如果用户的AREA_CODE为空，返回全国区县列表
            if (StringUtils.isEmpty(areaCode)) {
                logger.info("用户AREA_CODE为空，返回全国区县列表，userId: {}", userId);
                return getAreaTree();
            }

            // 根据用户的AREA_CODE构建包含本级和下属的层级结构
            logger.info("根据用户AREA_CODE构建层级结构，userId: {}, areaCode: {}", userId, areaCode);
            return buildAreaTreeFromCode(areaCode);

        } catch (Exception e) {
            logger.error("获取用户下属行政区划失败，userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据区划代码构建包含本级和下属的层级结构
     *
     * @param areaCode 区划代码
     * @return List<BasicArea>
     */
    private List<BasicArea> buildAreaTreeFromCode(String areaCode) {
        List<BasicArea> result = new ArrayList<>();

        // 根据代码查找本级区划
        Example example = new Example(BasicArea.class);
        example.createCriteria()
                .andEqualTo("yn", CommonConstant.FLAG_YES)
                .andEqualTo("code", areaCode);
        List<BasicArea> currentAreas = basicAreaMapper.selectByExample(example);

        if (currentAreas.isEmpty()) {
            logger.warn("未找到区划代码对应的区划信息，areaCode: {}", areaCode);
            return result;
        }

        BasicArea currentArea = currentAreas.get(0);

        // 获取下属区划并构建层级结构
        List<BasicArea> subordinateAreas = findByParentCode(areaCode);
        if (!subordinateAreas.isEmpty()) {
            // 如果有下属区划，需要进一步构建层级关系
            Map<String, List<BasicArea>> subordinateMap = new HashMap<>();

            // 按父级代码分组下属区划
            for (BasicArea area : subordinateAreas) {
                String parentCode = getParentCodeFromAreaCode(area.getCode());
                subordinateMap.computeIfAbsent(parentCode, k -> new ArrayList<>()).add(area);
            }

            // 为当前区划设置直接下属
            List<BasicArea> directChildren = subordinateMap.getOrDefault(areaCode, new ArrayList<>());

            // 为每个直接下属设置其子级
            for (BasicArea child : directChildren) {
                List<BasicArea> grandChildren = subordinateMap.getOrDefault(child.getCode(), new ArrayList<>());
                child.setChildren(grandChildren);
            }

            currentArea.setChildren(directChildren);
        }

        result.add(currentArea);
        return result;
    }

    /**
     * 根据区划代码获取父级代码
     *
     * @param areaCode 区划代码
     * @return 父级代码
     */
    private String getParentCodeFromAreaCode(String areaCode) {
        if (StringUtils.isEmpty(areaCode) || areaCode.length() < 6) {
            return "";
        }

        // 区县级（后两位不为00）-> 市级（后两位改为00）
        if (!areaCode.endsWith("00")) {
            return areaCode.substring(0, 4) + "00";
        }
        // 市级（中间两位不为00，后两位为00）-> 省级（中间两位改为00）
        else if (!areaCode.substring(2, 4).equals("00")) {
            return areaCode.substring(0, 2) + "0000";
        }
        // 省级或其他情况
        return "";
    }
    
    /**
     * 查询市场监督管理局列表
     *
     * @param vo 查询条件
     * @return PageInfo<Map<String, Object>>
     */
    @Override
    public PageInfo<Map<String, Object>> findMarketSupervisionList(MarketSupervisionVo vo) {
        if (vo.getPageNum() != null && vo.getPageSize() != null){
            PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        }
        orderBy(vo.getOrderColumn() + " " + vo.getOrderValue());
        List<Map<String, Object>> list = basicAreaMapper.findMarketSupervisionList(vo.getCityCode());
        return new PageInfo<>(list);
    }
}
