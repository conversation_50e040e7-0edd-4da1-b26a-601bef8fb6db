package com.jh.sme.dao.basic;

import com.jh.sme.bean.basic.BasicIndustry;
import com.jh.common.mybatis.BaseInfoMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 行业Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Mapper
public interface BasicIndustryMapper extends BaseInfoMapper<BasicIndustry> {

    /**
     * 验证行业大类是否存在
     *
     * @param majorCategoryName 行业大类名称
     * @return 存在的数量
     */
    int countMajorCategoryByName(@Param("majorCategoryName") String majorCategoryName);

}
