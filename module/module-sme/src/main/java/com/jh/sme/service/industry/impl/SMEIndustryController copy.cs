using Aspose.Cells;
using FD.Framework;
using FD.Framework.Data;
using FD.Model.Enum;
using FD.Model.SME;
using FD.Service.System;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using WebAPI.App_Code;
using WebAPI.FilterHelpers;
using WebAPI.Models.SME;

namespace WebAPI.Controllers
{
    /// <summary>
    /// 小微企业，行业
    /// </summary>
    public class SMEIndustryController : ApiController
    {


        protected SMELogHelper db = new SMELogHelper();




        #region 标准的增，删，改，查
        /// <summary>
        /// 取一条行业信息，小类
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]

        public IHttpActionResult Classify(int id)
        {
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";

            try
            {
                var record = db.DbUtil.Get<SMEIndustry>(id);
                if (record != null)
                {
                    result.code = 0;
                    result.data = record;
                    result.message = "操作成功";
                }
            }
            catch (Exception ex)
            {

                result.message = ex.ToString();
            }

            return Json(result);

        }


        /// <summary>
        /// 删除一条记录
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userid"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]

        public IHttpActionResult Del(int id, int userid)
        {
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";

            try
            {
                var record = db.DbUtil.Get<SMEIndustry>(id);
                record.IsDel = true;
                db.SaveLog<SMEIndustry>(record, LogType.Edit, "小微企业，行业", "删除行业:" + record.Name + "", userid);
                if (record != null)
                {
                    result.code = 0;
                    result.data = record;
                    result.message = "操作成功";
                }
            }
            catch (Exception ex)
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(new { id = id, userid = userid }));
                result.message = ex.ToString();
            }

            return Json(result);

        }


        /// <summary>
        /// 行业列表
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult List(IndustryQuery query)
        {

            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            query = SMEComm.QueryFilter(query) as IndustryQuery;

            result.code = -5;
            result.message = "系统错误";
            result.data = new { count = 0 };

            string field = @"p.name ParentName
                            ,case when a.attachmentCount is null then '未上传' else '已上传' end IsAttachment
                            , i.ID,i.Name,i.CityName,i.CountyName,i.Inputtime,i.YearTime";
            string table = @"SME_Industry i 
                            left join SME_Industry p on p.code = i.ParentCode
                            left join(select BusinessID, count(1) attachmentCount from SME_Attachment where tablename = 'SME_Industry' and BusinessType = 'DevelopmentFocus' and IsDel = 0 GROUP BY BusinessID ) a on i.id = a.BusinessID";
            string where = "where i.IsDel=0 and i.ParentCode<>'0' and i.ParentCode is not null";
            string order = "i.id";

            if (!string.IsNullOrEmpty(query.Name))
            {
                where += " and i.Name like  '%" + query.Name + "%'";
            }


            if (!string.IsNullOrEmpty(query.CityCode))
            {
                where += " and i.CityCode='" + query.CityCode + "'";
            }
            if (!string.IsNullOrEmpty(query.CountyCode))
            {
                where += " and i.CountyCode='" + query.CountyCode + "'";
            }





            System.Data.DataSet ds = MsSqlUtils.GetDataSet(field, table, order, where, query.PageSize, query.PageIndex);
            if (ds != null && ds.Tables.Count == 2 && ds.Tables[1].Rows.Count == 1)
            {
                result.code = 0;
                result.message = "操作成功";
                result.data = new { dataList = ds.Tables[0], count = ds.Tables[1].Rows[0][0] };
            }

            return Json(result);

        }





        /// <summary>
        /// 保存一条行业记录，小类
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="requestval"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult Save(dynamic requestval)
        {
            WebAPI.Models.JsonResult jsonResult = new WebAPI.Models.JsonResult();
            try
            {

                //SMEIndustry request = JsonConvert.DeserializeObject<SMEIndustry>(Convert.ToString(requestval));
                IndustryParam param = JsonConvert.DeserializeObject<IndustryParam>(Convert.ToString(requestval));
                SMEIndustry request = param.Model;
                AttachmentModel attachmentModel = new AttachmentModel();//用于更新附件表

                //2023-8-30获取当前的年份
                //string year = DateTime.Now.Year.ToString();
                //检查是否重复
                SMEIndustry model = null;
                if (request.Id == 0)//新增
                    model = db.DbUtil.GetT<SMEIndustry>(p => p.CountyCode == request.CountyCode && p.ParentCode == request.ParentCode && p.Name == request.Name && p.IsDel == false && p.YearTime == request.YearTime);
                else
                    model = db.DbUtil.GetT<SMEIndustry>(p => p.CountyCode == request.CountyCode && p.ParentCode == request.ParentCode && p.Name == request.Name && p.IsDel == false && p.Id != request.Id && p.YearTime == request.YearTime);
                if (model != null)
                {
                    jsonResult.code = -1;
                    jsonResult.message = "行业已存在";
                    return Json(jsonResult);
                }

                //
                var fileArray = requestval.DevelopmentFocus;

                SMEIndustry record;
                if (request.Id == 0)//新增
                {
                    record = new SMEIndustry();
                }
                else
                {
                    record = db.DbUtil.Get<SMEIndustry>(request.Id);
                }

                record.Name = request.Name;
                record.ParentCode = request.ParentCode;
                record.CityCode = request.CityCode;
                record.CityName = request.CityName;
                record.CountyCode = request.CountyCode;
                record.CountyName = request.CountyName;
                record.YearTime = request.YearTime;

                //存库操作
                if (request.Id == 0)//新增
                {

                    record.Code = MaxCode(request.ParentCode);
                    record.Inputtime = DateTime.Now;
                    record.IsDel = false;
                    record.UserID = request.UserID;
                    record.UserName = request.UserName;
                    // 用户名，
                    db.SaveLog<SMEIndustry>(record, LogType.Add, "小微企业，行业", "新增行业:" + record.Name + "", request.UserID);
                    var newRecord = db.DbUtil.GetT<SMEIndustry>(p => p.Inputtime == record.Inputtime && p.ParentCode == record.ParentCode && p.CountyCode == record.CountyCode && p.Code == record.Code);
                    if (newRecord != null) attachmentModel.BusinssId = newRecord.Id;


                }
                else
                {
                    if (string.IsNullOrEmpty(record.Code)) record.Code = MaxCode(request.ParentCode);
                    db.SaveLog<SMEIndustry>(record, LogType.Edit, "小微企业，行业", "修改行业:" + record.Name + "", request.UserID);
                    attachmentModel.BusinssId = record.Id;
                }

                //处理附件
                attachmentModel.FildIds = param.DevelopmentFocus;
                attachmentModel.TableName = "SME_Industry";
                attachmentModel.BusinessType = "DevelopmentFocus";
                SMEComm.UpdateAttachmentByType(attachmentModel);
                //更新企业行业关系
                UpdateEnterprise(attachmentModel.BusinssId, param.EnterpriseList);

                jsonResult.code = 0;
                jsonResult.data = record;
                jsonResult.message = "操作成功";
            }
            catch (Exception ex)
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(requestval));

                jsonResult.code = -5;
                jsonResult.message = ex.ToString();

            }


            return Json(jsonResult);

        }


        #endregion
        #region 相关接口 

        /// <summary>
        /// 行业大类
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public JsonResult Category()
        {
            JsonResult jsonResult = new JsonResult();

            string sql = @"SELECT code, name from SME_Industry where IsDel = 0 and ParentCode = '0' order by code";

            DataTable dt = MsSqlUtils.GetTable(sql);
            jsonResult.Data = dt;

            return jsonResult;
        }

        /// <summary>
        /// 根据行业小类查企业列表
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult EnterpriseList(int id)
        {

            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();


            result.code = -5;
            result.message = "系统错误";
            result.data = new { count = 0 };

            string sql = @"SELECT e.ID,e.Enterprise,e.USCC
                        from SME_Industry_Enterprise ie
                        left join SME_Enterprise e on e.id=ie.EnterpriseID
                        where ie.IndustryId=" + id;


            DataTable dt = MsSqlUtils.GetTable(sql);
            if (dt != null)
            {
                result.code = 0;
                result.message = "操作成功";
                result.data = dt;
            }

            return Json(result);

        }

        #endregion
        #region 辅助方法
        /// <summary>
        /// 更新企业行业关系
        /// 创建：季李刚  2022-05-01
        /// </summary>
        private static void UpdateEnterprise(int industryId, int[] enterpriseIds)
        {
            if (industryId == 0) return;
            string errInfo = "";

            try
            {
                string updateIds = string.Join(",", enterpriseIds);

                //1.删（
                string sql = "DELETE SME_Industry_Enterprise where IndustryID=" + industryId;
                SqlServerUtils.GetDataSet(sql);


                //2.update 传进来的记录，
                if (enterpriseIds != null)
                {
                    foreach (var item in enterpriseIds)
                    {
                        sql = "INSERT INTO [SME_Industry_Enterprise] ([IndustryID], [EnterpriseID], [Version]) VALUES ( {0},{1} , 1);";
                        sql = string.Format(sql, industryId, item);
                        SqlServerUtils.GetDataSet(sql);
                    }
                }

            }
            catch (Exception ex)
            {
                errInfo = ex.ToString();
            }
            finally
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, errInfo, JsonConvert.SerializeObject(new { industryId = industryId, enterpriseIds = enterpriseIds }));
            }

        }




        /// <summary>
        /// 取得最大CODE
        /// </summary>
        /// 创建：季李刚  2022-05-01
        /// <param name="parentCode"></param>
        /// <returns></returns>
        private string MaxCode(string parentCode)
        {
            if (string.IsNullOrEmpty(parentCode)) return null;
            int maxCode = 0;
            var maxRecord = db.DbUtil.GetTList<SMEIndustry>(p => p.ParentCode != null && p.ParentCode == parentCode).OrderByDescending(p => p.Code).FirstOrDefault();
            if (maxRecord != null)
            {
                if (!string.IsNullOrEmpty(maxRecord.Code))
                    int.TryParse(maxRecord.Code.Substring(parentCode.Length), out maxCode);
            }
            maxCode++;

            return parentCode + maxCode.ToString().PadLeft(5, '0');
        }
        #endregion


        #region 导入导出
        //导出前,先将导出数据都写入临时文件，并将临时文件的物理路径返回给前台（继续调用  Export）
        // public IHttpActionResult PreExport(EnterpriseQuery query)
        public IHttpActionResult PreExport(dynamic requestval)
        {

            IndustryQuery query = JsonConvert.DeserializeObject<IndustryQuery>(Convert.ToString(requestval));
            Models.JsonResult result = new Models.JsonResult();
            string msg = string.Empty;

            DataTable dt = ExportData(query);//1.取得要导出的数据
            string filepath = string.Empty;
            if (dt != null && dt.Rows.Count > 0)
                filepath = GetExportFile();//2.将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
            if (dt != null && dt.Rows.Count > 0 && !string.IsNullOrEmpty(filepath))
            {
                try
                {
                    //Message();
                    WriteExportFile(dt, filepath);//3.将数据写入临时文件
                }
                catch (Exception ex)
                {
                    msg = "操作文件时发生错误";
                    //HttpContext.Current.Server.MapPath("/UploadFile/Template/Aspose.Total.lic")
                    //string tempmsg = HttpContext.Current.Server.MapPath("/UploadFile/Template/Aspose.Total.lic");
                    //tempmsg += "\r\n" + ex.ToString();

                    string tempmsg = "\r\n" + ex.ToString();
                    ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, tempmsg, JsonConvert.SerializeObject(requestval));
                    //ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(requestval));
                }
            }
            else if (dt == null || dt.Rows.Count == 0)
                msg = "无有效数据可以导出";
            else if (string.IsNullOrEmpty(filepath))
                msg = "文件系统错误";

            //4.输出结果
            if (!string.IsNullOrEmpty(msg))//有异常
            {
                result.code = -1;
                result.message = msg;
            }
            else
            {
                var dicPath = filepath;
                filepath = filepath.Replace(HttpContext.Current.Server.MapPath("/"), "").Replace("\\", "/");
                filepath = HttpUtility.UrlEncode("/" + filepath);
                result.code = 0;
                result.message = "操作成功";
                result.data = filepath;
            }


            return Json(result);
        }


        /// <summary>
        /// 导出
        /// 创建：季李刚 2022-03-11
        /// </summary>
        /// <returns></returns>

        public System.Net.Http.HttpResponseMessage Export(string path)
        {
            return SMEComm.Export(path, "行业信息.xls");
        }






        /// <summary>
        /// 取得导出数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        private DataTable ExportData(IndustryQuery query)
        {
            DataTable dt = null;
            query = SMEComm.QueryFilter(query) as IndustryQuery;

            string field = @"p.name ParentName                           
                            , i.ID,i.Name,i.CityName,i.CountyName,i.Inputtime";
            string table = @"SME_Industry i 
                            left join SME_Industry p on p.code = i.ParentCode
                            ";
            string where = "where i.IsDel=0 and i.ParentCode<>'0' and i.ParentCode is not null";
            string order = "i.CityCode,i.CountyCode,i.ParentCode,i.Code";

            if (!string.IsNullOrEmpty(query.Name))
            {
                where += " and i.Name like  '%" + query.Name + "%'";
            }

            if (!string.IsNullOrEmpty(query.CityCode))
            {
                where += " and i.CityCode='" + query.CityCode + "'";
            }
            if (!string.IsNullOrEmpty(query.CountyCode))
            {
                where += " and i.CountyCode='" + query.CountyCode + "'";
            }


            dt = MsSqlUtils.CommonQuery(field, table, order, where);
            return dt;
        }


        /// <summary>
        /// 导出，将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
        /// </summary>
        /// <returns>返回临时文件的物理路径</returns>
        private string GetExportFile()
        {
            //string tempPath = TEMPATE_PATH;//模板路径 
            string tempPath = "/UploadFile/Template/行业模板.xls";//模板路径 
            return SMEComm.GetExportFile(tempPath);
        }

        /// <summary>
        /// 将数据写入导出模板
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="filepath"></param>
        private void WriteExportFile(DataTable dt, string filepath)
        {
            AsposeLicenseHelper asposeLicenseHelper = new AsposeLicenseHelper();
            asposeLicenseHelper.LicenseCell();//Aspose 授权
            Workbook workbook = new Workbook(filepath);
            Worksheet sheet1 = workbook.Worksheets[0];
            Cells cells = sheet1.Cells;


            for (int i = 0; i < dt.Rows.Count; i++)
            {
                DataRow dr = dt.Rows[i];
                int rowNumber = i + 2;
                cells["A" + rowNumber].PutValue(dr["CityName"].ToString());
                cells["B" + rowNumber].PutValue(dr["CountyName"].ToString());
                cells["C" + rowNumber].PutValue(dr["ParentName"].ToString());
                cells["D" + rowNumber].PutValue(dr["Name"].ToString());

            }

            workbook.Save(filepath);
        }

        //测试用
        private void Message()
        {

            try
            {
                //D:\网站\浙江省三同公共服务平台\UploadFile\Template\Aspose.Total.lic
                string cellfilepath = HttpContext.Current.Server.MapPath("/bin/Aspose.Cells.dll");
                System.Reflection.Assembly assembly = System.Reflection.Assembly.LoadFile(cellfilepath);
                System.Reflection.Assembly assembly2 = System.Reflection.Assembly.Load("Aspose.Cells");
                System.Reflection.AssemblyName name = assembly.GetName();

                string info = name.Version.ToString();
                ////HttpContext.Current.Server.MapPath("/UploadFile/Template/Aspose.Total.lic")
                //info += "\r\n"+ HttpContext.Current.Server.MapPath("/bin/Aspose.Cells.dll");
                info += "\r\n";
                info += "\r\n" + assembly2.GetName().Version.ToString(); ;
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, info, "");
            }
            catch (Exception ex)
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), "");

            }
        }


        /// <summary>
        /// 导入按钮的操作,将导入上传的文件存成一个临时文件
        /// 创建：季李刚 2022-02-24
        /// </summary>
        [ApiSMEAuthorize]

        public IHttpActionResult Import()
        {

            Models.JsonResult result = new Models.JsonResult();
            result.code = -1;
            string userId = HttpContext.Current.Request.QueryString["userid"];

            if (HttpContext.Current.Request.Files == null)
                result.message = "请勿上传空文件";

            if (HttpContext.Current.Request.Files.Count != 1)
                result.message = "只能上传一个文件";


            if (!string.IsNullOrEmpty(userId) && userId.ToIntEx() > 0)
                ;
            else
                result.message = "无权限";

            if (!string.IsNullOrEmpty(result.message))
                return Json(result);


            HttpPostedFile uploadFile = HttpContext.Current.Request.Files[0];
            string[] allowFileExt = { ".xls", ".xlsx" };

            var fileExt = Path.GetExtension(uploadFile.FileName);


            if (!allowFileExt.Contains(fileExt.ToLower()))
                result.message = "文件格式非法";

            if (uploadFile.ContentLength == 0)
                result.message = "请勿上传空文件";

            if (!string.IsNullOrEmpty(result.message))
                return Json(result);



            try
            {
                var path = "/UploadFile/SME/Industry" + DateTime.Now.ToString("yyyyMM") + "/" + DateTime.Now.ToString("dd") + "/";
                var mapPath = HttpContext.Current.Server.MapPath(path); //硬盘物理目录
                var fileName = Guid.NewGuid().ToString();

                var mapPathFullPath = mapPath + fileName + fileExt; //硬盘物理路径                          
                var siteFullPath = path + fileName + fileExt; //网站路径


                if (!Directory.Exists(mapPath))
                    Directory.CreateDirectory(mapPath);
                uploadFile.SaveAs(mapPathFullPath);


                result = ImportData(mapPathFullPath);

            }
            catch (Exception)
            {
                result.message = "保存文件错误！";
            }

            return Json(result);
        }

        /// <summary>
        /// 导入操作，将临时文件中的数据倒入到库中
        ///  创建：季李刚 2022-02-24
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="sequenceNo"></param>
        private Models.JsonResult ImportData(string fileFullName)
        {
            //string fileFullName = Server.MapPath("/UploadFiles/Excel/" + fileName);
            Models.JsonResult result = new Models.JsonResult();
            result.code = -1;
            string userId = HttpContext.Current.Request.QueryString["userid"];
            string username = HttpContext.Current.Request.QueryString["username"];



            DataTable dt = null;
            List<SMEIndustry> dataList = new List<SMEIndustry>();//表中读到的行业
            List<SMEIndustry> saveList = new List<SMEIndustry>();//成功存入的行业
            List<string> msgList = new List<string>();


            try
            {
                //dt = ExcelToDataTable(fileFullName);
                dt = GetExcelTable(fileFullName, "Sheet1");
            }
            catch (Exception ex)
            {
                result.message = ex.ToString();
                //HttpRuntime.Cache[cacheKey] = "导入错误";
                return result;
            }

            string sql = @"SELECT code,name from Basic_City where provinceId='330000'";
            DataTable dtCity = MsSqlUtils.GetTable(sql);

            sql = "SELECT code,name,cityId from SME_Basic_Area ";
            DataTable dtCounty = MsSqlUtils.GetTable(sql);

            sql = "SELECT code,name from SME_Industry where parentCode='0'";
            DataTable dtIndustry = MsSqlUtils.GetTable(sql);
            string yearTime = DateTime.Now.Year.ToString();

            if (dt != null && dt.Columns.Count > 0)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    DataRow item = dt.Rows[i];
                    string msg = string.Empty;
                    SMEIndustry model = new SMEIndustry();
                    try
                    {
                        //1. 读取
                        //{ "", "", "", "", "", "", "", "" };
                        model.CityName = item["市"].ToString().Trim();
                        model.CountyName = item["区/县"].ToString().Trim();
                        model.Name = item["行业小类"].ToString().Trim();
                        model.YearTime = item["帮扶时间"].ToString().Trim();
                        DataRow[] drCity = dtCity.Select("name='" + model.CityName + "'");
                        if (drCity != null && drCity.Count() == 1)
                            model.CityCode = drCity[0]["code"].ToString();
                        DataRow[] drCounty = dtCounty.Select("name='" + model.CountyName + "'"+"and cityId='"+ model.CityCode+"'");
                        if (drCounty != null && drCounty.Count() == 1)
                            model.CountyCode = drCounty[0]["code"].ToString();
                        DataRow[] drIndustry = dtIndustry.Select("name='" + item["行业大类"].ToString().Trim() + "'");
                        if (drIndustry != null && drIndustry.Count() == 1)
                        {
                            model.ParentCode = drIndustry[0]["code"].ToString();
                            model.Code = MaxCode(model.ParentCode);
                        }

                        if (string.IsNullOrEmpty(model.ParentCode))
                            continue;


                        // msg="企业名称为空";
                        //if (string.IsNullOrEmpty(model.USCC))
                        //    msg += model.Enterprise + "的统一社会信用代码为空";
                        //if (string.IsNullOrEmpty(model.Address))
                        //    msg += model.Enterprise + "企业地址的为空";

                        if (string.IsNullOrEmpty(model.CityName))
                            msg += model.Name + "的市为空";
                        if (string.IsNullOrEmpty(model.CountyName))
                            msg += model.Name + "的县(市、区）为空";
                        if (string.IsNullOrEmpty(model.CityCode))
                            msg += model.Name + "的市不存在";
                        if (string.IsNullOrEmpty(model.CountyCode))
                            msg += model.Name + "的县(市、区）不存在";
                        if (string.IsNullOrEmpty(model.ParentCode))
                            msg += model.Name + "的行业大类不存在";

                        if (!string.IsNullOrEmpty(msg))
                            msgList.Add(msg);
                        else
                        {
                            SMEComm.QueryFilter(model);//过滤非法字符
                            dataList.Add(model);
                        }

                    }
                    catch (Exception ex)
                    {
                        msgList.Add(ex.ToString());
                        continue;
                    }

                }

                if (msgList.Count > 0)
                {
                    result.message = "存在" + msgList.Count + "条数据错误,本次操作将不会存储任何数据。详情：";
                    result.message += string.Join("，", msgList);
                    return result;
                }

                //2.判重
                for (int i = 0; i < dataList.Count; i++)
                {
                    var model = dataList[i];
                    string msg = string.Empty;
                    var tempList = db.DbUtil.GetTList<SMEIndustry>(p => p.IsDel == false && p.Name == model.Name && p.ParentCode == model.ParentCode && p.YearTime == yearTime && p.CountyCode == model.CountyCode);
                    if (tempList != null && tempList.Count > 0)//判重
                    {
                        msg += model.Name + "行业已存在";
                        msgList.Add(msg);
                    }
                    else
                    {
                        saveList.Add(model);
                    }
                }


                //3.完善信息
                for (int i = 0; i < saveList.Count; i++)
                {
                    SMEIndustry model = saveList[i];
                    model.Inputtime = DateTime.Now;
                    model.YearTime = yearTime;
                    model.IsDel = false;
                    model.UserID = userId.ToIntEx();
                    model.UserName = username;
                }

                try
                {
                    //4. 并存表
                    db.SaveLog<SMEIndustry>(saveList, LogType.Add, "小微企业，企业", username, null);
                }
                catch (Exception)
                {
                    result.code = -5;
                    result.message = "系统错误";
                    return result;
                }
            }

            if (string.IsNullOrEmpty(result.message))
            {
                result.code = 0;
                result.message = "成功导入" + saveList.Count + "条数据。";

                if (msgList.Count > 0)
                    result.message += "以下记录被忽略(" + string.Join(",", msgList) + ")";

            }

            return result;
            //HttpRuntime.Cache[cacheKey] = "导入完成";

        }


        /// <summary>
        /// 获取Excel中的数据，根据Sheet名字获取数据
        /// </summary>
        /// <param Name="excelPath"></param>
        /// <param Name="sheetName"></param>
        /// <returns></returns>
        public static DataTable GetExcelTable(string excelPath, string sheetName)
        {
            if (!File.Exists(excelPath))
                throw new Exception("解析文件失败");

            DataTable dt = new DataTable();

            Workbook workbook = new Workbook(excelPath);
            Cells cells = workbook.Worksheets[sheetName].Cells;
            if (cells == null)
                throw new Exception("请将数据存于工作表：Sheet1");

            for (int i = 0; i < cells.MaxDataRow + 1; i++)
            {
                if (i == 0)
                {
                    for (int j = 0; j < cells.MaxDataColumn + 1; j++)
                    {
                        dt.Columns.Add(cells[i, j].StringValue.Trim());
                    }
                }
                else
                {
                    DataRow dr = dt.NewRow();
                    for (int j = 0; j < cells.MaxDataColumn + 1; j++)
                    {
                        dr[j] = cells[i, j].StringValue.Trim();
                    }
                    dt.Rows.Add(dr);
                }
            }

            //		行业大类	行业小类						
            string[] titles = { "市", "区/县", "行业大类", "行业小类", "帮扶时间" };
            foreach (string title in titles)
            {
                if (!dt.Columns.Contains(title))
                {
                    throw new Exception("模板无法解析");
                }
            }


            return dt;
        }

        public static DataTable ExcelToDataTable(string path)
        {
            DataTable dataTable = new DataTable();
            Workbook book = new Workbook(path);
            // Excel 中 sheets 数量必须大于 0
            if (book.Worksheets.Count > 0)
            {
                // 导入 Excel 文件中的第一个 sheets 工作表
                Cells cells = book.Worksheets[0].Cells;
                // sheets 中的数据必须存在
                if (cells.MaxDataRow != -1 && cells.MaxDataColumn != -1)
                {
                    // 方法 ExportDataTable 的参数说明
                    //  要导出的第一个单元格的行号。
                    //  要导出的第一个单元格的列号。
                    //  要导入的行数。
                    //  要导入的列数。
                    //  指示第一行的数据是否导出到DataTable的列名。
                    dataTable = cells.ExportDataTable(0, 0, cells.MaxDataRow + 1, cells.MaxDataColumn + 1, true);
                }
            }
            return dataTable;
        }

        #endregion

    }
}