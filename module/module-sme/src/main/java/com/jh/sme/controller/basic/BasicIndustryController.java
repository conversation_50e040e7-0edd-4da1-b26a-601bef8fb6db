package com.jh.sme.controller.basic;

import java.util.List;

import com.jh.common.annotation.ExportRespAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.jh.sme.bean.basic.BasicIndustry;
import com.jh.sme.bean.basic.vo.BasicIndustryVo;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.controller.BaseController;
import com.jh.sme.service.basic.BasicIndustryService;
import org.springframework.web.multipart.MultipartFile;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 行业Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/basic/industry")
@Tag(name = "行业")
public class BasicIndustryController extends BaseController {
    @Autowired
    private BasicIndustryService basicIndustryService;

    private static final String PER_PREFIX = "btn:basic:industry:";

    /**
     * 新增行业
     *
     * @param basicIndustry 行业数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增行业")
    @SystemLogAnnotation(type = "行业", value = "新增行业")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveBasicIndustry(@RequestBody BasicIndustryVo basicIndustry) {
        String id = basicIndustryService.saveOrUpdateBasicIndustry(basicIndustry);
        return RestApiResponse.ok(id);
    }


    /**
     * 批量删除行业(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除行业")
    @SystemLogAnnotation(type = "行业", value = "批量删除行业")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteBasicIndustry(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        basicIndustryService.deleteBasicIndustry(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询行业详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询行业详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        BasicIndustry basicIndustry = basicIndustryService.findById(id);
        return RestApiResponse.ok(basicIndustry);
    }

    /**
     * 分页查询行业
     *
     * @param basicIndustryVo 行业 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询行业")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody BasicIndustryVo basicIndustryVo) {
        PageInfo<BasicIndustry> basicIndustry = basicIndustryService.findPageByQuery(basicIndustryVo);
        return RestApiResponse.ok(basicIndustry);
    }

    /**
     * 查询行业大类列表
     *
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findMajorCategories")
    @Operation(summary = "查询行业大类列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findMajorCategories() {
        List<BasicIndustry> majorCategories = basicIndustryService.findMajorCategories();
        return RestApiResponse.ok(majorCategories);
    }

}
