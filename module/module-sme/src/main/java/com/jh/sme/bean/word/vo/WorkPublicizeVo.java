package com.jh.sme.bean.word.vo;

import com.jh.sme.bean.word.WorkPublicize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 宣传报道Vo
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Schema(description = "WorkPublicizeVo")
@Data
public class WorkPublicizeVo extends WorkPublicize {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    private List<FileInfo> fileList;

    /**
     * 文件信息内部类
     */
    @Data
    public static class FileInfo {
        private String fileName;
        private String fileUrl;
        private String filePath;
        private String url;
        private String name;
        private Long uid;
        private String status;
    }

}