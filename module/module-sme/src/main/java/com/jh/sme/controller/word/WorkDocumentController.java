package com.jh.sme.controller.word;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sme.bean.word.WorkDocument;
import com.jh.sme.bean.word.vo.WorkDocumentVo;
import com.jh.sme.service.word.WorkDocumentService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 政策文件Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/word/document")
@Tag(name = "政策文件")
public class WorkDocumentController extends BaseController {
    @Autowired
    private WorkDocumentService workDocumentService;

    private static final String PER_PREFIX = "btn:word:document:";

    /**
     * 新增政策文件
     *
     * @param workDocumentVo 政策文件数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增政策文件")
    @SystemLogAnnotation(type = "政策文件", value = "新增政策文件")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveWorkDocument(@RequestBody WorkDocumentVo workDocumentVo) {
        String id = workDocumentService.saveOrUpdateWorkDocument(workDocumentVo);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改政策文件
     *
     * @param workDocumentVo 政策文件数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改政策文件")
    @SystemLogAnnotation(type = "政策文件", value = "修改政策文件")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateWorkDocument(@RequestBody WorkDocumentVo workDocumentVo) {
        String id = workDocumentService.saveOrUpdateWorkDocument(workDocumentVo);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除政策文件(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除政策文件")
    @SystemLogAnnotation(type = "政策文件", value = "批量删除政策文件")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteWorkDocument(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        workDocumentService.deleteWorkDocument(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询政策文件详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询政策文件详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        WorkDocument workDocument = workDocumentService.findById(id);
        return RestApiResponse.ok(workDocument);
    }

    /**
     * 分页查询政策文件
     *
     * @param workDocumentVo 政策文件 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询政策文件")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody WorkDocumentVo workDocumentVo) {
        PageInfo<WorkDocument> workDocument = workDocumentService.findPageByQuery(workDocumentVo);
        return RestApiResponse.ok(workDocument);
    }

    /**
     * 查询所有政策文件列表
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findList")
    @Operation(summary = "查询所有政策文件列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findList')")
    public RestApiResponse<?> findList() {
        List<WorkDocument> list = workDocumentService.findList();
        return RestApiResponse.ok(list);
    }
}