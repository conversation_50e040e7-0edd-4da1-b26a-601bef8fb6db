package com.jh.sme.bean.basic;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 行业对象 basic_industry
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Table(name = "basic_industry")
@Schema(description = "行业")
public class BasicIndustry extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "CODE")
    private String code;
    @Column(name = "NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "Name")
    private String name;
    @Column(name = "PARENT_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "父类的CODE,0表示大类")
    private String parentCode;

    /**
     * SET CODE
     *
     * @param code
     */
    public void setCode(String code) {
        this.code = code == null ? null : code;
    }

    /**
     * GET CODE
     *
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * SET Name
     *
     * @param name
     */
    public void setName(String name) {
        this.name = name == null ? null : name;
    }

    /**
     * GET Name
     *
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * SET 父类的CODE,0表示大类
     *
     * @param parentCode
     */
    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode;
    }

    /**
     * GET 父类的CODE,0表示大类
     *
     * @return parentCode
     */
    public String getParentCode() {
        return parentCode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
