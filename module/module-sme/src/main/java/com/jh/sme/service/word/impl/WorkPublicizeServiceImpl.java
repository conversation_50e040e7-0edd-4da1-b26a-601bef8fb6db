package com.jh.sme.service.word.impl;

import com.jh.common.bean.SysRole;
import com.jh.common.bean.SysUser;
import com.jh.common.util.AppUserUtil;
import com.jh.sme.bean.basic.BasicFile;
import com.jh.sme.bean.word.WorkPublicize;
import com.jh.sme.bean.word.vo.WorkPublicizeVo;
import com.jh.sme.constant.RoleConstant;
import com.jh.sme.dao.basic.BasicFileMapper;
import com.jh.sme.dao.word.WorkPublicizeMapper;
import com.jh.sme.service.word.WorkPublicizeService;

import java.util.List;
import java.util.Date;
import java.util.ArrayList;

import com.jh.sys.dao.SysRoleMapper;
import com.jh.sys.dao.SysUserMapper;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 宣传报道Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
@Transactional(readOnly = true)
public class WorkPublicizeServiceImpl extends BaseServiceImpl<WorkPublicizeMapper, WorkPublicize> implements WorkPublicizeService {

    private static final Logger logger = LoggerFactory.getLogger(WorkPublicizeServiceImpl.class);
    @Autowired
    private WorkPublicizeMapper workPublicizeMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private BasicFileMapper basicFileMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新宣传报道
     *@param vo 宣传报道VO对象
     *@return String 宣传报道ID
     *<AUTHOR>
     */
    public String saveOrUpdateWorkPublicize(WorkPublicizeVo vo) {
        if (vo == null) {
            throw new ServiceException("数据异常");
        }

        // 判断是新增还是更新
        boolean isUpdate = !StringUtils.isEmpty(vo.getId());

        if (isUpdate) {
            // 更新操作
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            vo.setYn(null);
            workPublicizeMapper.updateByPrimaryKeySelective(vo);

            // 删除原有文件记录
            Example fileExample = new Example(BasicFile.class);
            Example.Criteria fileCriteria = fileExample.createCriteria();
            fileCriteria.andEqualTo("mainId", vo.getId());
            fileCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
            List<BasicFile> existingFiles = basicFileMapper.selectByExample(fileExample);
            for (BasicFile file : existingFiles) {
                BasicFile deleteFile = new BasicFile();
                deleteFile.setId(file.getId());
                deleteFile.setYn(CommonConstant.FLAG_NO);
                basicFileMapper.updateByPrimaryKeySelective(deleteFile);
            }
        } else {
            // 新增操作
            vo.setId(UUIDUtils.getUUID());
            workPublicizeMapper.insertSelective(vo);
        }

        // 保存文件信息
        if (vo.getFileList() != null && !vo.getFileList().isEmpty()) {
            int sortOrder = 1;
            for (WorkPublicizeVo.FileInfo fileInfo : vo.getFileList()) {
                BasicFile basicFile = new BasicFile();
                basicFile.setId(UUIDUtils.getUUID());
                basicFile.setMainId(vo.getId());
                basicFile.setFileName(fileInfo.getFileName());
                basicFile.setFileUrl(fileInfo.getFileUrl());
                basicFile.setFilePath(fileInfo.getFilePath());
                basicFile.setSortOrder(sortOrder++);
                basicFile.setYn(CommonConstant.FLAG_YES);
                basicFileMapper.insertSelective(basicFile);
            }
        }

        return vo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除宣传报道
     *@param ids void 宣传报道ID
     *<AUTHOR>
     */
    public void deleteWorkPublicize(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            WorkPublicize workPublicize = workPublicizeMapper.selectByPrimaryKey(id);
            if (workPublicize == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            WorkPublicize temWorkPublicize = new WorkPublicize();
            temWorkPublicize.setYn(CommonConstant.FLAG_NO);
            temWorkPublicize.setId(workPublicize.getId());
            workPublicizeMapper.updateByPrimaryKeySelective(temWorkPublicize);
        }
    }

    /**
     * 查询宣传报道详情
     *
     * @param id
     * @return WorkPublicizeVo
     * <AUTHOR>
     */
    @Override
    public WorkPublicizeVo findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        WorkPublicize workPublicize = workPublicizeMapper.selectByPrimaryKey(id);

        if (workPublicize != null) {
            // 查询关联的文件列表
            Example fileExample = new Example(BasicFile.class);
            Example.Criteria fileCriteria = fileExample.createCriteria();
            fileCriteria.andEqualTo("mainId", id);
            fileCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
            fileExample.orderBy("sortOrder").asc();
            List<BasicFile> fileList = basicFileMapper.selectByExample(fileExample);

            WorkPublicizeVo workPublicizeVo = new WorkPublicizeVo();
            BeanUtils.copyProperties(workPublicize, workPublicizeVo);

            if (fileList != null && !fileList.isEmpty()) {
                List<WorkPublicizeVo.FileInfo> fileInfoList = new ArrayList<>();
                for (BasicFile basicFile : fileList) {
                    WorkPublicizeVo.FileInfo fileInfo = new WorkPublicizeVo.FileInfo();
                    fileInfo.setFileName(basicFile.getFileName());
                    fileInfo.setFileUrl(basicFile.getFileUrl());
                    fileInfo.setFilePath(basicFile.getFilePath());
                    fileInfoList.add(fileInfo);
                }
                workPublicizeVo.setFileList(fileInfoList);
            }

            return workPublicizeVo;
        }

        return null;
    }

    /**
     * 分页查询宣传报道
     *
     * @param vo
     * @return PageInfo<WorkPublicize>
     * <AUTHOR>
     */
    @Override
    public PageInfo<WorkPublicize> findPageByQuery(WorkPublicizeVo vo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        orderBy(vo.getOrderColumn() + " " + vo.getOrderValue());

        Example example = new Example(WorkPublicize.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        // 检查用户角色权限
        checkUserRolePermission(criteria);


        // 添加查询条件
        if (!StringUtils.isEmpty(vo.getReportUnit())) {
            criteria.andLike("reportUnit", "%" + vo.getReportUnit() + "%");
        }

        // 行政区划代码查询
        if (!StringUtils.isEmpty(vo.getAreaCode())) {
            // 如果areaCode以00结尾，表示查询地级市下的所有数据，使用前4位进行模糊查询
            if (vo.getAreaCode().endsWith("00") && vo.getAreaCode().length() >= 6) {
                String cityCode = vo.getAreaCode().substring(0, 4);
                criteria.andLike("areaCode", cityCode + "%");
            } else {
                // 否则进行精确查询
                criteria.andEqualTo("areaCode", vo.getAreaCode());
            }
        }

        List<WorkPublicize> list = workPublicizeMapper.selectByExample(example);
        return new PageInfo<>(list);
    }


    /**
     * 检查用户角色权限
     *
     * @param criteria 查询条件
     */
    private void checkUserRolePermission(Criteria criteria) {
        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();

        // 获取当前用户角色列表
        List<SysRole> roleList = sysRoleMapper.listByUserId(userId);

        // 判断用户角色
        boolean hasProvincialRole = false;
        boolean hasCityRole = false;
        boolean hasCountyRole = false;
        boolean hasOrgRole = false;

        // 检查用户角色
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_PROVINCIAL.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) {
                    hasProvincialRole = true;
                } else if (RoleConstant.ROLE_CITY.equals(roleCode)) {
                    hasCityRole = true;
                } else if (RoleConstant.ROLE_COUNTY.equals(roleCode)) {
                    hasCountyRole = true;
                } else if (RoleConstant.ROLE_ORG_ADMIN.equals(roleCode)) {
                    hasOrgRole = true;
                }
            }
        }

        // 机构角色不允许查看数据
        if (hasOrgRole && !hasProvincialRole && !hasCityRole && !hasCountyRole) {
            throw new ServiceException("机构角色无权限查看数据");
        }

        // 如果是省级角色，可以查看所有数据，不添加额外条件
        if (hasProvincialRole) {
            return;
        }

        // 获取用户信息
        SysUser user = new SysUser();
        user.setId(userId);
        SysUser currentUser = sysUserMapper.selectOne(user);

        if (currentUser != null && currentUser.getAreaCode() != null) {
            String areaCode = currentUser.getAreaCode();

            if (hasCityRole) {
                // 市级角色：获取用户的areaCode，去掉末尾的00，进行模糊匹配
                if (areaCode.endsWith("00")) {
                    areaCode = areaCode.substring(0, areaCode.length() - 2);
                }
                criteria.andLike("areaCode", areaCode + "%");
            } else if (hasCountyRole) {
                // 区县角色：精确匹配areaCode
                criteria.andEqualTo("areaCode", areaCode);
            }
        }
    }


    /**
     * 查询所有宣传报道列表
     *
     * @return List<WorkPublicize>
     */
    @Override
    public List<WorkPublicize> findList() {
        Example example = new Example(WorkPublicize.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        return workPublicizeMapper.selectByExample(example);
    }

}