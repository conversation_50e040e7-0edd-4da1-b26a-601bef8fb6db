package com.jh.sme.controller.basic;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sme.bean.basic.BasicArea;
import com.jh.sme.bean.basic.vo.BasicAreaVo;
import com.jh.sme.bean.basic.vo.MarketSupervisionVo;
import com.jh.sme.service.basic.BasicAreaService;

import java.util.Map;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 浙江省行政区划Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/basic/area")
@Tag(name = "浙江省行政区划")
public class BasicAreaController extends BaseController {
    @Autowired
    private BasicAreaService basicAreaService;

    private static final String PER_PREFIX = "btn:basic:area:";

    /**
     * 新增浙江省行政区划
     *
     * @param basicArea 浙江省行政区划数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @Operation(summary = "新增浙江省行政区划")
    @SystemLogAnnotation(type = "浙江省行政区划", value = "新增浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveBasicArea(@RequestBody BasicArea basicArea) {
        String id = basicAreaService.saveOrUpdateBasicArea(basicArea);
        return RestApiResponse.ok(id);
    }

    /**
     * 修改浙江省行政区划
     *
     * @param basicArea 浙江省行政区划数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @Operation(summary = "修改浙江省行政区划")
    @SystemLogAnnotation(type = "浙江省行政区划", value = "修改浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateBasicArea(@RequestBody BasicArea basicArea) {
        String id = basicAreaService.saveOrUpdateBasicArea(basicArea);
        return RestApiResponse.ok(id);
    }

    /**
     * 批量删除浙江省行政区划(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除浙江省行政区划")
    @SystemLogAnnotation(type = "浙江省行政区划", value = "批量删除浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteBasicArea(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        basicAreaService.deleteBasicArea(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询浙江省行政区划详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询浙江省行政区划详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        BasicArea basicArea = basicAreaService.findById(id);
        return RestApiResponse.ok(basicArea);
    }

    /**
     * 分页查询浙江省行政区划
     *
     * @param basicAreaVo 浙江省行政区划 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询浙江省行政区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody BasicAreaVo basicAreaVo) {
        PageInfo<BasicArea> basicArea = basicAreaService.findPageByQuery(basicAreaVo);
        return RestApiResponse.ok(basicArea);
    }

    /**
     * 查询省级数据
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findProvinceList")
    @Operation(summary = "查询省级数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findProvinceList')")
    public RestApiResponse<?> findProvinceList() {
        List<BasicArea> list = basicAreaService.findProvinceList();
        return RestApiResponse.ok(list);
    }

    /**
     * 查询市级数据
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findCityList")
    @Operation(summary = "查询市级数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findCityList')")
    public RestApiResponse<?> findCityList() {
        List<BasicArea> list = basicAreaService.findCityList();
        return RestApiResponse.ok(list);
    }

    /**
     * 查询区县级数据
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findCountyList")
    @Operation(summary = "查询区县级数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findCountyList')")
    public RestApiResponse<?> findCountyList() {
        List<BasicArea> list = basicAreaService.findCountyList();
        return RestApiResponse.ok(list);
    }

    /**
     * 根据父级代码查询下级区划
     *
     * @param parentCode 父级代码
     * @return RestApiResponse<?>
     */
    @GetMapping("/findByParentCode")
    @Operation(summary = "根据父级代码查询下级区划")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findByParentCode')")
    public RestApiResponse<?> findByParentCode(@RequestParam("parentCode") String parentCode) {
        List<BasicArea> list = basicAreaService.findByParentCode(parentCode);
        return RestApiResponse.ok(list);
    }

    /**
     * 根据级别查询区划
     *
     * @param level 级别
     * @return RestApiResponse<?>
     */
    @GetMapping("/findByLevel")
    @Operation(summary = "根据级别查询区划")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findByLevel')")
    public RestApiResponse<?> findByLevel(@RequestParam("level") Long level) {
        List<BasicArea> list = basicAreaService.findByLevel(level);
        return RestApiResponse.ok(list);
    }

    /**
     * 获取省市区县树形结构
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/getAreaTree")
    @Operation(summary = "获取省市区县树形结构")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getAreaTree')")
    public RestApiResponse<?> getAreaTree() {
        List<BasicArea> list = basicAreaService.getAreaTree();
        return RestApiResponse.ok(list);
    }

    /**
     * 根据用户行政区划获取下属区划列表
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/getUserSubordinateAreas")
    @Operation(summary = "根据用户行政区划获取下属区划列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getUserSubordinateAreas')")
    public RestApiResponse<?> getUserSubordinateAreas() {
        List<BasicArea> list = basicAreaService.getUserSubordinateAreas(getCurrentUserId());
        return RestApiResponse.ok(list);
    }
    
    /**
     * 查询市场监督管理局列表
     *
     * @param marketSupervisionVo 查询条件
     * @return RestApiResponse<?>
     */
    @PostMapping("/findMarketSupervisionList")
    @Operation(summary = "查询市场监督管理局列表")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findMarketSupervisionList')")
    public RestApiResponse<?> findMarketSupervisionList(@RequestBody MarketSupervisionVo marketSupervisionVo) {
        PageInfo<Map<String, Object>> list = basicAreaService.findMarketSupervisionList(marketSupervisionVo);
        return RestApiResponse.ok(list);
    }
}
