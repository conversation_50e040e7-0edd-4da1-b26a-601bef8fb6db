package com.jh.sme.bean.basic.vo;

import com.jh.sme.bean.basic.BasicArea;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 浙江省行政区划Vo
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Schema(description = "BasicAreaVo")
@Data
public class BasicAreaVo extends BasicArea {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    @Schema(description = "排序列")
    private String orderColumn = "sort";

    @Schema(description = "排序方式")
    private String orderValue = "asc";

    @Schema(description = "父级区划代码")
    private String parentCode;

    @Schema(description = "是否只查询省级")
    private Boolean onlyProvince;

    @Schema(description = "是否只查询市级")
    private Boolean onlyCity;

    @Schema(description = "是否只查询区县级")
    private Boolean onlyCounty;
}