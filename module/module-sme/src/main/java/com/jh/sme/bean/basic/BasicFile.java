package com.jh.sme.bean.basic;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 文件管理附件对象 basic_file
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Table(name = "basic_file")
@Schema(description = "文件管理附件")
@Data
public class BasicFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "MAIN_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件管理主表ID")
    private String mainId;
    @Column(name = "FILE_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件名称")
    private String fileName;
    @Column(name = "FILE_URL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件URL")
    private String fileUrl;
    @Column(name = "FILE_PATH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "文件路径")
    private String filePath;
    @Column(name = "SORT_ORDER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @Schema(description = "排序")
    private Integer sortOrder;

}
