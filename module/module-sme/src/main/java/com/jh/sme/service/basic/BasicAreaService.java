package com.jh.sme.service.basic;

import java.util.List;
import java.util.Map;
import com.github.pagehelper.PageInfo;
import com.jh.sme.bean.basic.BasicArea;
import com.jh.sme.bean.basic.vo.BasicAreaVo;
import com.jh.sme.bean.basic.vo.MarketSupervisionVo;
import org.springframework.web.multipart.MultipartFile;
/**
 * 行业基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface BasicAreaService{
	/**
	 * 保存或更新行业基础信息
	 *@param basicArea 行业基础信息对象
	 *@return String 行业基础信息ID
	 *<AUTHOR>
	 */
	String saveOrUpdateBasicArea(BasicArea basicArea);
	
	/**
	 * 删除行业基础信息
	 *@param ids void 行业基础信息ID
	 *<AUTHOR>
	 */
	void deleteBasicArea(List<String> ids);

	/**
	 * 查询行业基础信息详情
	 *@param id
	 *@return BasicArea
	 *<AUTHOR>
	 */
	BasicArea findById(String id);

	/**
	 * 分页查询行业基础信息
	 *@param basicAreaVo
	 *@return PageInfo<BasicArea>
	 *<AUTHOR>
	 */
	PageInfo<BasicArea> findPageByQuery(BasicAreaVo basicAreaVo);
	
	/**
     * 查询省级数据
     * @return List<BasicArea>
     */
    List<BasicArea> findProvinceList();
    
    /**
     * 查询市级数据
     * @return List<BasicArea>
     */
    List<BasicArea> findCityList();
    
    /**
     * 查询区县级数据
     * @return List<BasicArea>
     */
    List<BasicArea> findCountyList();
    
    /**
     * 根据父级代码查询下级区划
     * @param parentCode 父级代码
     * @return List<BasicArea>
     */
    List<BasicArea> findByParentCode(String parentCode);
    
    /**
     * 根据级别查询区划
     * @param level 级别
     * @return List<BasicArea>
     */
    List<BasicArea> findByLevel(Long level);
    
    /**
     * 获取省市区县树形结构
     * @return List<BasicArea>
     */
    List<BasicArea> getAreaTree();
    
    /**
     * 根据用户ID获取用户下属的行政区划列表（包含本级和下属的层级结构）
     * @param userId 用户ID
     * @return List<BasicArea>
     */
    List<BasicArea> getUserSubordinateAreas(String userId);
    
    /**
     * 查询市场监督管理局列表
     * @param marketSupervisionVo 查询条件
     * @return PageInfo<Map<String, Object>>
     */
    PageInfo<Map<String, Object>> findMarketSupervisionList(MarketSupervisionVo marketSupervisionVo);

}
