package com.jh.sme.bean.word;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.jh.sme.bean.basic.BasicFile;

/**
 * 政策文件对象 work_document
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Table(name = "work_document")
@Schema(description = "政策文件")
@Data
public class WorkDocument extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "POLICY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策文件名称")
    private String policyName;

    @Column(name = "POLICY_NUMBER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "政策文号")
    private String policyNumber;

    @Column(name = "CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "内容概要")
    private String content;

    @Column(name = "ISSUE_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "发文日期")
    private Date issueDate;

    @Column(name = "IMPLEMENTATION_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "实施日期")
    private Date implementationDate;

    @Column(name = "VALID_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "有效日期")
    private Date validDate;

    @Column(name = "SUBSIDY")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @Schema(description = "财政补贴金额")
    private BigDecimal subsidy;

    @Column(name = "AREA_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划代码")
    private String areaCode;

    @Column(name = "AREA_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划名称")
    private String areaName;


}