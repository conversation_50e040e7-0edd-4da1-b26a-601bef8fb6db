package com.jh.sme.service.basic.impl;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.sme.bean.basic.BasicFile;
import com.jh.sme.bean.basic.vo.BasicFileVo;
import com.jh.sme.dao.basic.BasicFileMapper;
import com.jh.sme.service.basic.BasicFileService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件管理附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
@Transactional(readOnly = true)
public class BasicFileServiceImpl extends BaseServiceImpl<BasicFileMapper, BasicFile> implements BasicFileService {

    private static final Logger logger = LoggerFactory.getLogger(BasicFileServiceImpl.class);
    @Autowired
    private BasicFileMapper basicFileMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新文件管理附件
     *@param basicFile 文件管理附件对象
     *@return String 文件管理附件ID
     *<AUTHOR>
     */
    public String saveOrUpdateBasicFile(BasicFile basicFile) {
        if (basicFile == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(basicFile.getId())) {
            //新增
            basicFile.setId(UUIDUtils.getUUID());
            basicFileMapper.insertSelective(basicFile);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            basicFile.setYn(null);
            basicFileMapper.updateByPrimaryKeySelective(basicFile);
        }
        return basicFile.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除文件管理附件
     *@param ids void 文件管理附件ID
     *<AUTHOR>
     */
    public void deleteBasicFile(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            BasicFile basicFile = basicFileMapper.selectByPrimaryKey(id);
            if (basicFile == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            BasicFile tembasicFile = new BasicFile();
            tembasicFile.setYn(CommonConstant.FLAG_NO);
            tembasicFile.setId(basicFile.getId());
            basicFileMapper.updateByPrimaryKeySelective(tembasicFile);
        }
    }

    /**
     * 查询文件管理附件详情
     *
     * @param id
     * @return BasicFile
     * <AUTHOR>
     */
    @Override
    public BasicFile findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return basicFileMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询文件管理附件
     *
     * @param basicFileVo
     * @return PageInfo<BasicFile>
     * <AUTHOR>
     */
    @Override
    public PageInfo<BasicFile> findPageByQuery(BasicFileVo basicFileVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(basicFileVo.getPageNum(), basicFileVo.getPageSize());
        orderBy(basicFileVo.getOrderColumn() + " " + basicFileVo.getOrderValue());
        Example example = getExample(basicFileVo);
        List<BasicFile> basicFileList = basicFileMapper.selectByExample(example);
        return new PageInfo<BasicFile>(basicFileList);
    }

    private Example getExample(BasicFileVo basicFileVo) {
        Example example = new Example(BasicFile.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(basicFileVo.getName())){
        //	criteria.andEqualTo(basicFileVo.getName());
        //}
        example.orderBy("updateTime").desc();
        return example;
    }

}
