<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sme.dao.basic.BasicAreaMapper">

    <!-- 查询省级数据 -->
    <select id="findProvinceList" resultType="com.jh.sme.bean.basic.BasicArea">
        SELECT *
        FROM basic_area
        WHERE LEVEL = 0
          AND YN = 1
        ORDER BY SORT ASC
    </select>

    <!-- 查询市级数据 -->
    <select id="findCityList" resultType="com.jh.sme.bean.basic.BasicArea">
        SELECT *
        FROM basic_area
        WHERE LEVEL = 1
          AND YN = 1
        ORDER BY SORT ASC
    </select>

    <!-- 查询区县级数据 -->
    <select id="findCountyList" resultType="com.jh.sme.bean.basic.BasicArea">
        SELECT *
        FROM basic_area
        WHERE LEVEL = 2
          AND YN = 1
        ORDER BY SORT ASC
    </select>

    <!-- 根据父级代码查询下级区划 -->
    <select id="findByParentCode" resultType="com.jh.sme.bean.basic.BasicArea" parameterType="java.lang.String">
        SELECT *
        FROM basic_area
        WHERE YN = 1
        <choose>
            <!-- 如果是省级代码（6位，后4位为0000），查询市级 -->
            <when test="parentCode != null and parentCode.length() == 6 and parentCode.endsWith('0000')">
                AND CODE LIKE CONCAT(SUBSTRING(#{parentCode}, 1, 2), '%')
                AND LEVEL = 1
            </when>
            <!-- 如果是市级代码（4位，后2位为00），查询区县级 -->
            <when test="parentCode != null and parentCode.length() == 4 and parentCode.endsWith('00')">
                AND CODE LIKE CONCAT(#{parentCode}, '%')
                AND LEVEL = 2
            </when>
            <!-- 如果是市级代码（6位，后2位为00），查询区县级 -->
            <when test="parentCode != null and parentCode.length() == 6 and parentCode.endsWith('00')">
                AND CODE LIKE CONCAT(SUBSTRING(#{parentCode}, 1, 4), '%')
                AND LEVEL = 2
            </when>
            <!-- 其他情况，按原逻辑查询 -->
            <otherwise>
                AND CODE LIKE CONCAT(#{parentCode}, '%')
                AND LENGTH(CODE) > LENGTH(#{parentCode})
            </otherwise>
        </choose>
        ORDER BY SORT ASC
    </select>

    <!-- 根据级别查询区划 -->
    <select id="findByLevel" resultType="com.jh.sme.bean.basic.BasicArea" parameterType="java.lang.Long">
        SELECT *
        FROM basic_area
        WHERE LEVEL = #{level}
          AND YN = 1
        ORDER BY SORT ASC
    </select>

    <!-- 根据城市名称获取城市代码（限制在浙江省） -->
    <select id="getCityCodeByName" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT CODE
        FROM basic_area
        WHERE NAME = #{cityName}
          AND LEVEL = 1
          AND CODE LIKE '33%'
          AND YN = 1
        LIMIT 1
    </select>

    <!-- 根据区县名称和城市代码获取区县代码（限制在浙江省） -->
    <select id="getCountyCodeByName" resultType="java.lang.String">
        SELECT CODE
        FROM basic_area
        WHERE NAME = #{countyName}
          AND CODE LIKE CONCAT(SUBSTRING(#{cityCode}, 1, 4), '%')
          AND CODE LIKE '33%'
          AND LEVEL = 2
          AND YN = 1
        LIMIT 1
    </select>

    <!-- 查询市场监督管理局列表 -->
    <select id="findMarketSupervisionList" resultType="java.util.Map">
        SELECT 
            CASE 
                WHEN a.LEVEL = 1 THEN CONCAT(a.NAME, '市场监督管理局')
                WHEN a.LEVEL = 2 THEN CONCAT(
                    COALESCE(
                        (SELECT c.NAME FROM basic_area c WHERE c.CODE = CONCAT(SUBSTRING(a.CODE, 1, 4), '00') AND c.YN = 1 LIMIT 1), 
                        ''
                    ), 
                    a.NAME, 
                    '市场监督管理局'
                )
                ELSE CONCAT(a.NAME, '市场监督管理局')
            END as name,
            a.CODE as code
        FROM basic_area a
        WHERE a.YN = 1
          AND a.CODE LIKE '33%'
          AND a.LEVEL IN (1, 2)
        <if test="cityCode != null and cityCode != ''">
            AND (
                (a.LEVEL = 1 AND a.CODE = #{cityCode}) OR
                (a.LEVEL = 2 AND a.CODE LIKE CONCAT(SUBSTRING(#{cityCode}, 1, 4), '%'))
            )
        </if>
        ORDER BY a.CODE
    </select>

</mapper>