package com.jh.sme.service.basic.impl;

import com.jh.utils.ImportService;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jh.sme.bean.basic.BasicIndustry;
import com.jh.sme.bean.basic.vo.BasicIndustryVo;
import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;
import com.jh.sme.dao.basic.BasicIndustryMapper;
import com.jh.sme.service.basic.BasicIndustryService;
import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

/**
 * 行业Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
@Transactional(readOnly = true)
public class BasicIndustryServiceImpl extends BaseServiceImpl<BasicIndustryMapper, BasicIndustry> implements BasicIndustryService {

    private static final Logger logger = LoggerFactory.getLogger(BasicIndustryServiceImpl.class);
    @Autowired
    private BasicIndustryMapper basicIndustryMapper;

    @Autowired
    private ImportService importService;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新行业
     *@param basicIndustry 行业对象
     *@return String 行业ID
     *<AUTHOR>
     */
    public String saveOrUpdateBasicIndustry(BasicIndustry basicIndustry) {
        if (basicIndustry == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(basicIndustry.getId())) {
            //新增
            basicIndustry.setId(UUIDUtils.getUUID());
            basicIndustryMapper.insertSelective(basicIndustry);
        } else {
            //TODO 做判断后方能执行修改 一般判断是否是自己的数据
            //避免页面传入修改
            basicIndustry.setYn(null);
            basicIndustryMapper.updateByPrimaryKeySelective(basicIndustry);
        }
        return basicIndustry.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除行业
     *@param ids void 行业ID
     *<AUTHOR>
     */
    public void deleteBasicIndustry(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除 一般判断是否是自己的数据
            BasicIndustry basicIndustry = basicIndustryMapper.selectByPrimaryKey(id);
            if (basicIndustry == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            BasicIndustry tembasicIndustry = new BasicIndustry();
            tembasicIndustry.setYn(CommonConstant.FLAG_NO);
            tembasicIndustry.setId(basicIndustry.getId());
            basicIndustryMapper.updateByPrimaryKeySelective(tembasicIndustry);
        }
    }

    /**
     * 查询行业详情
     *
     * @param id
     * @return BasicIndustry
     * <AUTHOR>
     */
    @Override
    public BasicIndustry findById(String id) {
        // TODO 做判断后方能反回数据 一般判断是否是自己的数据
        return basicIndustryMapper.selectByPrimaryKey(id);
    }


    /**
     * 分页查询行业
     *
     * @param basicIndustryVo
     * @return PageInfo<BasicIndustry>
     * <AUTHOR>
     */
    @Override
    public PageInfo<BasicIndustry> findPageByQuery(BasicIndustryVo basicIndustryVo) {
        // TODO 做判断后方能执行查询 一般判断是否是自己的数据
        PageHelper.startPage(basicIndustryVo.getPageNum(), basicIndustryVo.getPageSize());
        orderBy(basicIndustryVo.getOrderColumn() + " " + basicIndustryVo.getOrderValue());
        Example example = getExample(basicIndustryVo);
        List<BasicIndustry> basicIndustryList = basicIndustryMapper.selectByExample(example);
        return new PageInfo<BasicIndustry>(basicIndustryList);
    }

    private Example getExample(BasicIndustryVo basicIndustryVo) {
        Example example = new Example(BasicIndustry.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(basicIndustryVo.getName())){
        //	criteria.andEqualTo(basicIndustryVo.getName());
        //}
        example.orderBy("updateTime").desc();
        return example;
    }

    /**
     * 查询行业大类列表（yn=1且PARENT_CODE=0）
     *
     * @return List<BasicIndustry>
     * <AUTHOR>
     */
    @Override
    public List<BasicIndustry> findMajorCategories() {
        Example example = new Example(BasicIndustry.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("parentCode", CommonConstant.FLAG_NO);
        example.orderBy("code").asc();
        return basicIndustryMapper.selectByExample(example);
    }

}
