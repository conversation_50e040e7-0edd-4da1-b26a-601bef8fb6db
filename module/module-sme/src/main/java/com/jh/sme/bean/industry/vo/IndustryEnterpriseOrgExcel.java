package com.jh.sme.bean.industry.vo;


import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * 企业机构帮扶关联对象 industry_enterprise_org
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class IndustryEnterpriseOrgExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "主表ID", index = 0)
    private String applyId;
    @ExcelProperty(value = "机构用户ID", index = 1)
    private String userId;
    @ExcelProperty(value = "机构代码", index = 2)
    private String orgCode;
    @ExcelProperty(value = "企业ID", index = 3)
    private String enterpriseId;
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 4)
    private String importSituation;

    public String getImportSituation() {
        return importSituation;
    }

    public void setImportSituation(String importSituation) {
        this.importSituation = importSituation;
    }

    /**
     * SET 主表ID
     *
     * @param applyId
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId;
    }

    /**
     * GET 主表ID
     *
     * @return applyId
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * SET 机构用户ID
     *
     * @param userId
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId;
    }

    /**
     * GET 机构用户ID
     *
     * @return userId
     */
    public String getUserId() {
        return userId;
    }

    /**
     * SET 机构代码
     *
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode;
    }

    /**
     * GET 机构代码
     *
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 企业ID
     *
     * @param enterpriseId
     */
    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId == null ? null : enterpriseId;
    }

    /**
     * GET 企业ID
     *
     * @return enterpriseId
     */
    public String getEnterpriseId() {
        return enterpriseId;
    }
}
