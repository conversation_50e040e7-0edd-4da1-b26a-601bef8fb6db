package com.jh.sme.bean.industry.vo;


import com.jh.sme.bean.industry.IndustryList;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * 提升行业清单Vo
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Schema(description = "IndustryListVo")
@Data
public class IndustryListVo extends IndustryList {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    private List<FileInfo> fileList;

    /**
     * 文件信息内部类
     */
    @Data
    public static class FileInfo {
        private String fileName;
        private String fileUrl;
        private String filePath;
        private String url;
        private String name;
        private Long uid;
        private String status;
    }


    @Schema(description = "相关企业列表")
    private List<enterpriseList> enterpriseList;

    @Data
    public static class enterpriseList {
        private String enterpriseId;
    }

}