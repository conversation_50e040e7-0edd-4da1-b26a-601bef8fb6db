package com.jh.sme.service.word;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.sme.bean.word.WorkPublicize;
import com.jh.sme.bean.word.vo.WorkPublicizeVo;

/**
 * 宣传报道Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface WorkPublicizeService {
    /**
     * 保存或更新宣传报道
     *
     * @param workPublicizeVo 宣传报道VO对象
     * @return String 宣传报道ID
     * <AUTHOR>
     */
    String saveOrUpdateWorkPublicize(WorkPublicizeVo workPublicizeVo);

    /**
     * 删除宣传报道
     *
     * @param ids void 宣传报道ID
     * <AUTHOR>
     */
    void deleteWorkPublicize(List<String> ids);

    /**
     * 查询宣传报道详情
     *
     * @param id
     * @return WorkPublicizeVo
     * <AUTHOR>
     */
    WorkPublicizeVo findById(String id);

    /**
     * 分页查询宣传报道
     *
     * @param workPublicizeVo
     * @return PageInfo<WorkPublicize>
     * <AUTHOR>
     */
    PageInfo<WorkPublicize> findPageByQuery(WorkPublicizeVo workPublicizeVo);

    /**
     * 查询所有宣传报道列表
     *
     * @return List<WorkPublicize>
     */
    List<WorkPublicize> findList();

}