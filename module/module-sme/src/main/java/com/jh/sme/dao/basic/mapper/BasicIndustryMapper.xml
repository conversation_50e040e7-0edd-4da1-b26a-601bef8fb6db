<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jh.sme.dao.basic.BasicIndustryMapper">

    <!-- 验证行业大类是否存在 -->
    <select id="countMajorCategoryByName" resultType="int" parameterType="java.lang.String">
        SELECT COUNT(1)
        FROM basic_industry
        WHERE NAME = #{majorCategoryName}
          AND PARENT_CODE = '0'
          AND YN = 1
    </select>

</mapper>