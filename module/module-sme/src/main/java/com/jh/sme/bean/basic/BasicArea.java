package com.jh.sme.bean.basic;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import java.util.List;

/**
 * 浙江省行政区划对象 basic_area
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Table(name = "basic_area")
@Schema(description = "浙江省行政区划")
@Data
public class BasicArea extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划代码")
    private String code;

    @Column(name = "NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划名称")
    private String name;

    @Column(name = "LEVEL")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "级别 0省级1市级2区县级")
    private Long level;

    @Column(name = "SORT")
    @ColumnType(jdbcType = JdbcType.BIGINT)
    @Schema(description = "排序")
    private Long sort;

    @Transient
    @Schema(description = "子区域列表")
    private List<BasicArea> children;

}
