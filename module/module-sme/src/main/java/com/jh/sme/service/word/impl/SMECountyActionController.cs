using Aspose.Cells;
using FD.Framework.Data;
using FD.Model.Enum;
using FD.Model.SME;
using FD.Service.System;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using WebAPI.App_Code;
using WebAPI.FilterHelpers;
using WebAPI.Models.SME;

namespace WebAPI.Controllers
{
    /// <summary>
    /// 地市帮扶活动表
    /// </summary>
    public class SMECountyActionController : ApiController
    {


        protected SMELogHelper db = new SMELogHelper();

        #region 标准的增，删，改，查

        /// <summary>
        /// 存一条记录
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="requestval"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult Save(dynamic requestval) { 
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";


            try
            {
                CountyActionParam param = JsonConvert.DeserializeObject<CountyActionParam>(Convert.ToString(requestval));

                SMECountyAction request = param.Model;
                int businessId = 0;
                SMECountyAction record;
                if (request.Id == 0)//新增
                {
                    record = new SMECountyAction();
                }
                else
                {
                    record = db.DbUtil.Get<SMECountyAction>(request.Id);
                }

                record.IsEnd = request.IsEnd;
                record.StartDate = request.StartDate;
                record.ActionSubject = request.ActionSubject;
                record.ActionType = request.ActionType;
                record.ActionTypeOther = request.ActionTypeOther;
                record.PeopleNumber = request.PeopleNumber;
                record.About = request.About;

                if (record.IsEnd)
                    record.EndDate = request.EndDate;
                else
                    record.EndDate = null;


                record.CityCode = request.CityCode;
                record.CityName = request.CityName;
                record.CountyCode = request.CountyCode;
                record.CountyName = request.CountyName;



                //存库操作
                if (request.Id == 0)//新增
                {
                    record.IsDel = false;
                    record.Inputtime = DateTime.Now;
                    record.UserID = request.UserID;
                    record.UserName = request.UserName;
           
                    db.SaveLog<SMECountyAction>(record, LogType.Add, "小微企业，县区帮扶活动", "新增县区帮扶活动:" + record.CityName + "-"+record.CountyName, request.UserID);

                    var newRecord = db.DbUtil.GetT<SMECountyAction>(p => p.Inputtime == record.Inputtime );
                    if (newRecord != null) businessId = newRecord.Id;                    

                }
                else
                {

                    db.SaveLog<SMECountyAction>(record, LogType.Edit, "小微企业，县区帮扶活动", "修改县区帮扶活动:" + record.CityName + "-" + record.CountyName, request.UserID);
                    businessId = record.Id;
                }

                // 操作附件
                if (businessId > 0)
                {
                    AttachmentModel attachmentModel = new AttachmentModel();//用于更新附件表
                    attachmentModel.BusinssId = businessId;
                    attachmentModel.TableName = "SME_County_Action";


                    //其他见证材料
                    attachmentModel.BusinessType = "OtherFiles";
                    attachmentModel.FildIds = param.OtherFiles;
                    SMEComm.UpdateAttachmentByType(attachmentModel);

                    //更新活动企业关系
                    UpdateEnterprise(attachmentModel.BusinssId, param.EnterpriseList);

                    //TODO 组织方
                    UpdateOrganizer(businessId, param.OrganizerList);

                }





                result.code = 0;
                result.data = record;
                result.message = "操作成功";
            }
            catch (Exception ex)
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(requestval));
                result.code = -5;
                result.message = ex.ToString();

            }


            return Json(result);

        }



        /// <summary>
        /// 删除
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userid"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult Del(int id, int userid)
        {
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";

            try
            {
                var record = db.DbUtil.Get<SMECountyAction>(id);
                record.IsDel = true;
                db.SaveLog<SMECountyAction>(record, LogType.Edit, "小微企业，县区帮扶", "删除县区帮扶:" + record.Id + "", userid);
                if (record != null)
                {
                    result.code = 0;
                    result.data = record;
                    result.message = "操作成功";
                }
            }
            catch (Exception ex)
            {

                result.message = ex.ToString();
            }

            return Json(result);

        }



        /// <summary>
        /// 取一个市级活动
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult Action(int id)
        {
            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            result.code = -5;
            result.message = "系统错误";

            try
            {
                var record = db.DbUtil.Get<SMECountyAction>(id);
                if (record != null)
                {
                    result.code = 0;
                    result.data = record;
                    result.message = "操作成功";
                }
            }
            catch (Exception ex)
            {

                result.message = ex.ToString();
            }

            return Json(result);

        }

        /// <summary>
        /// 市级帮扶列表
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult List(CountyActionQuery query)
        {
            var aa = HttpContext.Current.Request;



            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();
            // query = SMEComm.QueryFilter(query) as CountyActionQuery;

            result.code = -5;
            result.message = "系统错误";
            result.data = new { count = 0 };

            string field = "[ID],[ActionSubject],  [CityName],[CountyName],Inputtime ";
            string table = "SME_County_Action";
            string where = "where IsDel = 0 ";
            string order = "id";
          


            if (!string.IsNullOrEmpty(query.CityCode))
            {
                where += " and CityCode='" + query.CityCode + "'";
            }
            if (!string.IsNullOrEmpty(query.CountyCode))
            {
                where += " and CountyCode='" + query.CountyCode + "'";
            }


            //TODO 年月


            System.Data.DataSet ds = MsSqlUtils.GetDataSet(field, table, order, where, query.PageSize, query.PageIndex);
            if (ds != null && ds.Tables.Count == 2 && ds.Tables[1].Rows.Count == 1)
            {
                result.code = 0;
                result.message = "操作成功";
                result.data = new { dataList = ds.Tables[0], count = ds.Tables[1].Rows[0][0] };
            }

            return Json(result);

        }

        #endregion
        #region 相关接口 


        /// <summary>
        /// 根据县级活动查企业列表
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult EnterpriseList(int id)
        {

            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();


            result.code = -5;
            result.message = "系统错误";
            result.data = new { count = 0 };

            string sql = @"SELECT e.ID,e.Enterprise,e.USCC
                        from SME_County_Action_Enterprise ie
                        left join SME_Enterprise e on e.id=ie.EnterpriseID
                        where ie.ActionID=" + id;


            DataTable dt = MsSqlUtils.GetTable(sql);
            if (dt != null)
            {
                result.code = 0;
                result.message = "操作成功";
                result.data = dt;
            }

            return Json(result);

        }



        /// <summary>
        /// 活动的组织方
        /// 创建：季李刚  2022-06-06
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult ActionMOrgList(int id)
        {

            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();


            result.code = -5;
            result.message = "系统错误";
            result.data = new { count = 0 };

            string sql = @"SELECT  OrganizerName as Name,OrganizerCode as Code,Type from SME_County_Action_Org
                            where ActionID=" + id;


            DataTable dt = MsSqlUtils.GetTable(sql);
            if (dt != null)
            {
                result.code = 0;
                result.message = "操作成功";
                result.data = dt;
            }

            return Json(result);

        }


        /// <summary>
        /// 全省市场监督管理局
        /// 创建：季李刚  2022-05-01
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [ApiSMEAuthorize]
        public IHttpActionResult MorgList(MorgQuery query)
        {

            WebAPI.Models.JsonResult result = new WebAPI.Models.JsonResult();


            result.code = -5;
            result.message = "系统错误";
            result.data = new { count = 0 };

            string sql = @"SELECT ISNULL(c.cityname, '') +a.name+'市场监督管理局' as name ,a.code,row_number() over (ORDER BY a.code)  as numbers from SME_Basic_Area a
                    LEFT JOIN (SELECT name cityname,code citycode from Basic_City where provinceid='330000') c on c.citycode=a.cityid
                    where a.cityid<>0 
                    ORDER BY a.code";


            string field = "ISNULL(c.cityname, '') +a.name+'市场监督管理局' as name ,a.code";
            string table = "SME_Basic_Area a  LEFT JOIN(SELECT name cityname, code citycode from Basic_City where provinceid = '330000') c on c.citycode = a.cityid";
            string where = "where a.cityid<>0";
            string order = "a.code";

            if (!string.IsNullOrEmpty(query.CityCode))
            {
                where += " and (a.cityid='" + query.CityCode + "' or a.code='" + query.CityCode + "')";
            }

            





            System.Data.DataSet ds = MsSqlUtils.GetDataSet(field, table, order, where, query.PageSize, query.PageIndex);
            if (ds != null && ds.Tables.Count == 2 && ds.Tables[1].Rows.Count == 1)
            {
                result.code = 0;
                result.message = "操作成功";
                result.data = new { dataList = ds.Tables[0], count = ds.Tables[1].Rows[0][0] };
            }




            //DataTable dt = MsSqlUtils.GetTable(sql);
            //if (dt != null)
            //{
            //    result.code = 0;
            //    result.message = "操作成功";
            //    result.data = dt;
            //}

            return Json(result);

        }




        #endregion

        #region 导入导出
        //导出前,先将导出数据都写入临时文件，并将临时文件的物理路径返回给前台（继续调用  Export）
        // public IHttpActionResult PreExport(EnterpriseQuery query)
        public IHttpActionResult PreExport(dynamic requestval)
        {

            CountyActionQuery query = JsonConvert.DeserializeObject<CountyActionQuery>(Convert.ToString(requestval));

            Models.JsonResult result = new Models.JsonResult();
            string msg = string.Empty;

            DataTable dt = ExportData(query);//1.取得要导出的数据
            string filepath = string.Empty;
            if (dt != null && dt.Rows.Count > 0)
                filepath = GetExportFile();//2.将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
            if (dt != null && dt.Rows.Count > 0 && !string.IsNullOrEmpty(filepath))
            {
                try
                {
                    WriteExportFile(dt, filepath);//3.将数据写入临时文件
                }
                catch (Exception ex)
                {
                    msg = "操作文件时发生错误";
                    ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, ex.ToString(), JsonConvert.SerializeObject(requestval));
                }
            }
            else if (dt == null || dt.Rows.Count == 0)
                msg = "无有效数据可以导出";
            else if (string.IsNullOrEmpty(filepath))
                msg = "文件系统错误";

            //4.输出结果
            if (!string.IsNullOrEmpty(msg))//有异常
            {
                result.code = -1;
                result.message = msg;
            }
            else
            {
                var dicPath = filepath;
                filepath = filepath.Replace(HttpContext.Current.Server.MapPath("/"), "").Replace("\\", "/");
                filepath = HttpUtility.UrlEncode("/" + filepath);
                result.code = 0;
                result.message = "操作成功";
                result.data = filepath;
            }


            return Json(result);
        }


        /// <summary>
        /// 导出
        /// 创建：季李刚 2022-03-11
        /// </summary>
        /// <returns></returns>

        //public System.Net.Http.HttpResponseMessage Export(EnterpriseQuery query)
        public System.Net.Http.HttpResponseMessage Export(string path)
        {

            return SMEComm.Export(path, "县级帮扶.xls");
        }






        /// <summary>
        /// 取得导出数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        private DataTable ExportData(CountyActionQuery query)
        {
            DataTable dt = null;
            query = SMEComm.QueryFilter(query) as CountyActionQuery;

            string field = "*,ROW_NUMBER() OVER(ORDER BY id) rownumber";
            string table = "SME_County_Action";
            string where = "where IsDel=0";
            string order = "id";


            if (!string.IsNullOrEmpty(query.CityCode))
            {
                where += " and CityCode='" + query.CityCode + "'";
            }
            if (!string.IsNullOrEmpty(query.CountyCode))
            {
                where += " and CountyCode='" + query.CountyCode + "'";
            }


            dt = MsSqlUtils.CommonQuery(field, table, order, where);
            return dt;
        }


        /// <summary>
        /// 导出，将模板复制一份到临时文件，供EXCECL操作，返回临时文件的物理路径
        /// </summary>
        /// <returns>返回临时文件的物理路径</returns>
        private string GetExportFile()
        {
            string tempPath = "/UploadFile/Template/县级帮扶模板.xls";//模板路径 

            return SMEComm.GetExportFile(tempPath);

        }

        /// <summary>
        /// 将数据写入导出模板
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="filepath"></param>
        private void WriteExportFile(DataTable dt, string filepath)
        {
            AsposeLicenseHelper asposeLicenseHelper = new AsposeLicenseHelper();
            asposeLicenseHelper.LicenseCell();//Aspose 授权
            Workbook workbook = new Workbook(filepath);
            Worksheet sheet1 = workbook.Worksheets[0];
            Cells cells = sheet1.Cells;

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                DataRow dr = dt.Rows[i];
                int rowNumber = i + 2;
                cells["A" + rowNumber].PutValue(dr["rownumber"].ToString());
                cells["B" + rowNumber].PutValue(dr["CityName"].ToString());
                cells["C" + rowNumber].PutValue(dr["CountyName"].ToString());

                string tempDate = string.Empty;
                if (!string.IsNullOrEmpty(dr["StartDate"].ToString()))
                    tempDate = DateTime.Parse(dr["StartDate"].ToString()).ToString("yyyy-MM-dd");

                if (dr["IsEnd"] != null && dr["IsEnd"].ToString().Length > 0 && bool.Parse(dr["IsEnd"].ToString()))
                {
                    if (!string.IsNullOrEmpty(dr["EndDate"].ToString()))
                        tempDate +=" - "+ DateTime.Parse(dr["EndDate"].ToString()).ToString("yyyy-MM-dd");
                }
                if(!string.IsNullOrEmpty(tempDate)) 
                    cells["D" + rowNumber].PutValue(tempDate);

                cells["E" + rowNumber].PutValue(dr["ActionSubject"].ToString());

                tempDate = string.Empty;
                tempDate=dr["ActionType"].ToString();
                if (tempDate=="其他" && (string.IsNullOrEmpty(dr["ActionTypeOther"].ToString())==false))
                {
                    tempDate += "（" + dr["ActionTypeOther"].ToString() + "）";
                }
                cells["F" + rowNumber].PutValue(tempDate+"");
                //组织方
                tempDate = string.Empty;
                tempDate = ExportMorgList(Convert.ToInt32(dr["ID"].ToString()));
                cells["G" + rowNumber].PutValue(tempDate + "");


                cells["H" + rowNumber].PutValue(dr["PeopleNumber"].ToString());
                cells["I" + rowNumber].PutValue(dr["About"].ToString());

                //相关企业
                tempDate = string.Empty;
                tempDate = ExportEnterpriseList(Convert.ToInt32(dr["ID"].ToString()));
                cells["J" + rowNumber].PutValue(tempDate + "");
                


            }

            workbook.Save(filepath);
        }


        //导出，一条记录的组织方
        private string ExportMorgList(int id)
        {
            string result = string.Empty;

            string sql = @"SELECT  OrganizerName as Name,OrganizerCode as Code,Type from SME_County_Action_Org
                            where ActionID=" + id;


            DataTable dt = MsSqlUtils.GetTable(sql);
            if (dt != null)
            {
                List<string> arrayName = new List<string>();
                foreach (DataRow dr in dt.Rows)
                {
                    arrayName.Add(dr["Name"].ToString());
                }
                result = string.Join("，", arrayName.ToArray());
            }

            return result + "";
        }


        //导出，一条记录的相关企业
        private string ExportEnterpriseList(int id)
        {
            string result = string.Empty;

            string sql = @"SELECT e.ID,e.Enterprise,e.USCC
                        from SME_County_Action_Enterprise ie
                        left join SME_Enterprise e on e.id=ie.EnterpriseID
                        where ie.ActionID=" + id;


            DataTable dt = MsSqlUtils.GetTable(sql);
            if (dt != null)
            {
                List<string> arrayName = new List<string>();
                foreach (DataRow dr in dt.Rows)
                {
                    arrayName.Add(dr["Enterprise"].ToString());
                }
                result = string.Join("，", arrayName.ToArray());
            }

            return result + "";
        }




        #endregion

        #region 辅助方法

        /// <summary>
        /// 更新活动企业关系
        /// 创建：季李刚  2022-05-01
        /// </summary>
        private static void UpdateEnterprise(int actionId, int[] enterpriseIds)
        {
            if (actionId == 0) return;
            string errorInfo = "";
            try
            {
                string updateIds = string.Join(",", enterpriseIds);

                //1.删（
                string sql = "DELETE SME_County_Action_Enterprise where ActionID=" + actionId;
                SqlServerUtils.GetDataSet(sql);


                //2.update 传进来的记录，
                if (enterpriseIds != null)
                {
                    foreach (var item in enterpriseIds)
                    {
                        sql = "INSERT INTO [SME_County_Action_Enterprise] ([ActionID], [EnterpriseID], [Version]) VALUES ( {0},{1} , 1);";
                        sql = string.Format(sql, actionId, item);
                        SqlServerUtils.GetDataSet(sql);
                    }
                }

            }
            catch (Exception ex)
            {
                errorInfo = ex.ToString();
            }
            finally { 
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, errorInfo, JsonConvert.SerializeObject(new { actionId = actionId , enterpriseIds = enterpriseIds }));

            }

        }


        /// <summary>
        /// 更新组织方
        /// 创建：季李刚  2022-06-06
        /// </summary>
        private static void UpdateOrganizer(int actionId, List<CountyOrganizer> list)
        {
            if (actionId == 0) return;
            string errorInfo = "";
            try
            {
                string updateIds = string.Join(",", list.Select(p=>p.ActionID).ToArray());

                //1.删（
                string sql = "DELETE SME_County_Action_Org where ActionID=" + actionId;
                SqlServerUtils.GetDataSet(sql);


                //2.update 传进来的记录，
                if (list != null)
                {
                    foreach (var item in list)
                    {
                        sql = "INSERT INTO [SME_County_Action_Org] ([ActionID], [OrganizerName],[OrganizerCode],[Type], [Version]) VALUES ( {0},'{1}','{2}',{3} , 1);";
                        sql = string.Format(sql, actionId,item.Name,item.Code,item.Type, item);
                        SqlServerUtils.GetDataSet(sql);
                    }
                }

            }
            catch (Exception ex)
            {
                errorInfo = ex.ToString();
            }
            finally
            {
                ErrLogs.AnsycLog(System.Reflection.MethodBase.GetCurrentMethod().Name, errorInfo, JsonConvert.SerializeObject(new { actionId = actionId, Organizers = list }));

            }

        }
        #endregion



    }
}