package com.jh.sme.service.industry;

import java.util.List;

import com.github.pagehelper.PageInfo;

import com.jh.sme.bean.industry.IndustryList;
import com.jh.sme.bean.industry.vo.IndustryListVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 提升行业清单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IndustryListService {
    /**
     * 保存或更新提升行业清单
     *
     * @param industryList 提升行业清单对象
     * @return String 提升行业清单ID
     * <AUTHOR>
     */
    String saveOrUpdateIndustryList(IndustryList industryList);

    /**
     * 删除提升行业清单
     *
     * @param ids void 提升行业清单ID
     * <AUTHOR>
     */
    void deleteIndustryList(List<String> ids);

    /**
     * 查询提升行业清单详情
     *
     * @param id
     * @return IndustryList
     * <AUTHOR>
     */
    IndustryList findById(String id);

    /**
     * 分页查询提升行业清单
     *
     * @param industryListVo
     * @return PageInfo<IndustryList>
     * <AUTHOR>
     */
    PageInfo<IndustryList> findPageByQuery(IndustryListVo industryListVo);

    /**
     * 按条件导出查询提升行业清单
     *
     * @param industryListVo
     * @return PageInfo<IndustryList>
     * <AUTHOR>
     */
    List<IndustryList> findByQuery(IndustryListVo industryListVo);

    /**
     * 导入提升行业清单
     *
     * @param file
     * @param cover 是否覆盖 1 覆盖 0 不覆盖
     * @return
     * <AUTHOR>
     */
    void importIndustryListAsync(MultipartFile file, Integer cover);

    /**
     * 保存提升行业清单（包含文件处理）
     *
     * @param industryListVo 提升行业清单VO对象（包含文件列表）
     * @return String 提升行业清单ID
     * <AUTHOR>
     */
    String saveIndustryListWithFiles(IndustryListVo industryListVo);
}
