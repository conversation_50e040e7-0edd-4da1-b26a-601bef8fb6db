package com.jh.sme.bean.industry;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 提升行业清单对象 industry_list
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Table(name = "industry_list")
@Schema(description = "提升行业清单")
@Data
public class IndustryList extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "MAJOR_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业大类")
    private String majorCategory;

    @Column(name = "MINOR_CATEGORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行业小类")
    private String minorCategory;

    @Column(name = "CITY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "市")
    private String city;

    @Column(name = "COUNTY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "县/区")
    private String county;

    @Column(name = "AREA_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划CODE")
    private String areaCode;

    @Column(name = "AREA_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "")
    private String areaName;

    @Column(name = "SUPPORT_YEAR")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "帮扶时间（年份）")
    private String supportYear;
}
