package com.jh.sme.bean.industry.vo;


import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * 企业导入对象 industry_enterprise
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class IndustryEnterpriseExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "市", index = 0)
    private String city;
    @ExcelProperty(value = "县(市、区)", index = 1)
    private String county;
    @ExcelProperty(value = "行业大类", index = 2)
    private String majorCategory;
    @ExcelProperty(value = "行业小类", index = 3)
    private String minorCategory;
    @ExcelProperty(value = "企业类型", index = 4)
    private String enterpriseType;
    @ExcelProperty(value = "企业名称", index = 5)
    private String enterpriseName;
    @ExcelProperty(value = "统一社会信用代码", index = 6)
    private String uscc;
    @ExcelProperty(value = "企业地址", index = 7)
    private String address;
    @ExcelProperty(value = "联系人", index = 8)
    private String contactPerson;
    @ExcelProperty(value = "联系方式", index = 9)
    private String contactPhone;
    @ExcelProperty(value = "帮扶时间", index = 10)
    private String helpYear;
    /**
     * 导入情况
     */
    @ExcelProperty(value = "导入情况", index = 11)
    private String importSituation;

    public String getImportSituation() {
        return importSituation;
    }

    public void setImportSituation(String importSituation) {
        this.importSituation = importSituation;
    }

    /**
     * SET 市
     *
     * @param city
     */
    public void setCity(String city) {
        this.city = city == null ? null : city;
    }

    /**
     * GET 市
     *
     * @return city
     */
    public String getCity() {
        return city;
    }

    /**
     * SET 县(市、区)
     *
     * @param county
     */
    public void setCounty(String county) {
        this.county = county == null ? null : county;
    }

    /**
     * GET 县(市、区)
     *
     * @return county
     */
    public String getCounty() {
        return county;
    }

    /**
     * SET 行业大类
     *
     * @param majorCategory
     */
    public void setMajorCategory(String majorCategory) {
        this.majorCategory = majorCategory == null ? null : majorCategory;
    }

    /**
     * GET 行业大类
     *
     * @return majorCategory
     */
    public String getMajorCategory() {
        return majorCategory;
    }

    /**
     * SET 行业小类
     *
     * @param minorCategory
     */
    public void setMinorCategory(String minorCategory) {
        this.minorCategory = minorCategory == null ? null : minorCategory;
    }

    /**
     * GET 行业小类
     *
     * @return minorCategory
     */
    public String getMinorCategory() {
        return minorCategory;
    }

    /**
     * SET 企业类型
     *
     * @param enterpriseType
     */
    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    /**
     * GET 企业类型
     *
     * @return enterpriseType
     */
    public String getEnterpriseType() {
        return enterpriseType;
    }

    /**
     * SET 统一社会信用代码
     *
     * @param uscc
     */
    public void setUscc(String uscc) {
        this.uscc = uscc == null ? null : uscc;
    }

    /**
     * GET 统一社会信用代码
     *
     * @return uscc
     */
    public String getUscc() {
        return uscc;
    }

    /**
     * SET 企业名称
     *
     * @param enterpriseName
     */
    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName == null ? null : enterpriseName;
    }

    /**
     * GET 企业名称
     *
     * @return enterpriseName
     */
    public String getEnterpriseName() {
        return enterpriseName;
    }

    /**
     * SET 企业地址
     *
     * @param address
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address;
    }

    /**
     * GET 企业地址
     *
     * @return address
     */
    public String getAddress() {
        return address;
    }

    /**
     * SET 联系人
     *
     * @param contactPerson
     */
    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson == null ? null : contactPerson;
    }

    /**
     * GET 联系人
     *
     * @return contactPerson
     */
    public String getContactPerson() {
        return contactPerson;
    }

    /**
     * SET 联系方式
     *
     * @param contactPhone
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone;
    }

    /**
     * GET 联系方式
     *
     * @return contactPhone
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * SET 帮扶时间
     *
     * @param helpYear
     */
    public void setHelpYear(String helpYear) {
        this.helpYear = helpYear == null ? null : helpYear;
    }

    /**
     * GET 帮扶时间
     *
     * @return helpYear
     */
    public String getHelpYear() {
        return helpYear;
    }
}
