package com.jh.sme.service.industry.impl;


import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.LoginUser;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.io.FileUtil;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.enums.ImportStatusEnum;
import com.jh.sme.bean.industry.IndustryEnterprise;
import com.jh.sme.bean.industry.IndustryEnterpriseOrg;
import com.jh.sme.bean.industry.vo.IndustryEnterpriseExcel;
import com.jh.sme.bean.industry.vo.IndustryEnterpriseVo;
import com.jh.sme.dao.industry.IndustryEnterpriseMapper;
import com.jh.sme.dao.industry.IndustryEnterpriseOrgMapper;
import com.jh.sys.dao.SysUserMapper;
import com.jh.sys.dao.SysRoleMapper;
import com.jh.common.bean.SysUser;
import com.jh.common.bean.SysRole;
import com.jh.sme.constant.RoleConstant;
import com.jh.sme.service.industry.IndustryEnterpriseService;
import com.jh.utils.ImportService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.jh.common.exception.ServiceException;
import com.jh.common.service.BaseServiceImpl;
import com.jh.common.util.security.UUIDUtils;

import com.jh.common.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.github.pagehelper.page.PageMethod.orderBy;

import org.springframework.web.multipart.MultipartFile;

/**
 * 企业导入Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
@Transactional(readOnly = true)
public class IndustryEnterpriseServiceImpl extends BaseServiceImpl<IndustryEnterpriseMapper, IndustryEnterprise> implements IndustryEnterpriseService {

    private static final Logger logger = LoggerFactory.getLogger(IndustryEnterpriseServiceImpl.class);
    @Autowired
    private IndustryEnterpriseMapper industryEnterpriseMapper;

    @Autowired
    private IndustryEnterpriseOrgMapper industryEnterpriseOrgMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private ImportService importService;

    @Autowired
    private com.jh.sme.dao.basic.BasicAreaMapper basicAreaMapper;

    @Autowired
    private com.jh.sme.dao.basic.BasicIndustryMapper basicIndustryMapper;

    @Value("${file.temp.path}")
    private String tempPath;

    @Override
    @Transactional(readOnly = false)
    /**
     * 保存或更新企业导入
     *@param vo 企业导入VO对象
     *@return String 企业导入ID
     *<AUTHOR>
     */
    public String saveOrUpdateIndustryEnterprise(IndustryEnterpriseVo vo) {
        if (vo == null) {
            throw new ServiceException("数据异常");
        }
        if (vo.getEnterpriseType() == null) {
            throw new ServiceException("类型不正确");
        }

        // 判重逻辑：检查是否重复
        Example duplicateExample = new Example(IndustryEnterprise.class);
        Criteria criteria = duplicateExample.createCriteria();
        criteria.andEqualTo("areaCode", vo.getAreaCode())
                .andEqualTo("enterpriseType", vo.getEnterpriseType())
                .andEqualTo("helpYear", vo.getHelpYear())
                .andEqualTo("yn", CommonConstant.FLAG_YES);
        
        // 企业名称或统一社会信用代码重复检查（OR条件）
        if (StringUtils.hasText(vo.getEnterpriseName()) && StringUtils.hasText(vo.getUscc())) {
            // 如果企业名称和统一社会信用代码都有值，检查任一匹配
            criteria.andCondition("(enterprise_name = '" + vo.getEnterpriseName() + "' OR uscc = '" + vo.getUscc() + "')");
        } else if (StringUtils.hasText(vo.getEnterpriseName())) {
            // 只有企业名称
            criteria.andEqualTo("enterpriseName", vo.getEnterpriseName());
        } else if (StringUtils.hasText(vo.getUscc())) {
            // 只有统一社会信用代码
            criteria.andEqualTo("uscc", vo.getUscc());
        }
        
        // 如果是更新操作，排除当前记录
        if (StringUtils.hasText(vo.getId())) {
            criteria.andNotEqualTo("id", vo.getId());
        }
        
        List<IndustryEnterprise> duplicateList = industryEnterpriseMapper.selectByExample(duplicateExample);
        if (!CollectionUtils.isEmpty(duplicateList)) {
            throw new ServiceException("企业已存在");
        }

        // 创建企业实体对象
        IndustryEnterprise industryEnterprise = new IndustryEnterprise();
        BeanUtils.copyProperties(vo, industryEnterprise);

        if (StringUtils.isEmpty(vo.getId())) {
            //新增
            String enterpriseId = UUIDUtils.getUUID();
            industryEnterprise.setId(enterpriseId);

            industryEnterprise.setCreateUser(AppUserUtil.getCurrentUserName());
            industryEnterprise.setCreateUserNickname(AppUserUtil.getCurrentRealName());
            industryEnterprise.setUpdateUser(AppUserUtil.getCurrentUserName());
            industryEnterprise.setUpdateUserNickname(AppUserUtil.getCurrentRealName());
            industryEnterprise.setCreateTime(new Date());
            industryEnterprise.setUpdateTime(new Date());
            // 根据企业类型设置企业类型名称
            if (industryEnterprise.getEnterpriseType() != null) {
                if (industryEnterprise.getEnterpriseType() == 1) {
                    industryEnterprise.setEnterpriseTypeName("体系导入企业");
                } else if (industryEnterprise.getEnterpriseType() == 2) {
                    industryEnterprise.setEnterpriseTypeName("培育类企业");
                }
            }
            industryEnterpriseMapper.insertSelective(industryEnterprise);

            // 处理机构关联信息
            if (!CollectionUtils.isEmpty(vo.getOrgList())) {
                for (IndustryEnterpriseVo.orgList orgItem : vo.getOrgList()) {
                    IndustryEnterpriseOrg enterpriseOrg = new IndustryEnterpriseOrg();
                    enterpriseOrg.setId(UUIDUtils.getUUID());
                    enterpriseOrg.setEnterpriseId(enterpriseId);
                    enterpriseOrg.setOrgCode(orgItem.getOrgCode());
                    enterpriseOrg.setUserId(orgItem.getOrgId());
                    enterpriseOrg.setYn(CommonConstant.FLAG_YES);
                    industryEnterpriseOrgMapper.insertSelective(enterpriseOrg);
                }
            }
        } else {
            // 验证数据是否存在
            IndustryEnterprise existingEnterprise = industryEnterpriseMapper.selectByPrimaryKey(vo.getId());
            if (existingEnterprise == null) {
                throw new ServiceException("数据不存在");
            }
            //避免页面传入修改
            industryEnterprise.setYn(null);

            industryEnterprise.setUpdateUser(AppUserUtil.getCurrentUserName());
            industryEnterprise.setUpdateUserNickname(AppUserUtil.getCurrentRealName());
            industryEnterprise.setUpdateTime(new Date());
            // 根据企业类型设置企业类型名称
            if (industryEnterprise.getEnterpriseType() != null) {
                if (industryEnterprise.getEnterpriseType() == 1) {
                    industryEnterprise.setEnterpriseTypeName("体系导入企业");
                } else if (industryEnterprise.getEnterpriseType() == 2) {
                    industryEnterprise.setEnterpriseTypeName("培育类企业");
                }
            }
            industryEnterpriseMapper.updateByPrimaryKeySelective(industryEnterprise);

            // 更新时先删除旧的机构关联，再插入新的
            if (!CollectionUtils.isEmpty(vo.getOrgList())) {
                // 逻辑删除旧的关联记录
                IndustryEnterpriseOrg deleteOrg = new IndustryEnterpriseOrg();
                deleteOrg.setYn(CommonConstant.FLAG_NO);
                Example deleteExample = new Example(IndustryEnterpriseOrg.class);
                deleteExample.createCriteria()
                        .andEqualTo("enterpriseId", vo.getId())
                        .andEqualTo("yn", CommonConstant.FLAG_YES);
                industryEnterpriseOrgMapper.updateByExampleSelective(deleteOrg, deleteExample);

                // 插入新的关联记录
                for (IndustryEnterpriseVo.orgList orgItem : vo.getOrgList()) {
                    IndustryEnterpriseOrg enterpriseOrg = new IndustryEnterpriseOrg();
                    enterpriseOrg.setId(UUIDUtils.getUUID());
                    enterpriseOrg.setEnterpriseId(vo.getId());
                    enterpriseOrg.setOrgCode(orgItem.getOrgCode());
                    enterpriseOrg.setUserId(orgItem.getOrgId());
                    enterpriseOrg.setYn(CommonConstant.FLAG_YES);
                    industryEnterpriseOrgMapper.insertSelective(enterpriseOrg);
                }
            }
        }
        return industryEnterprise.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * 删除企业导入
     *@param ids void 企业导入ID
     *<AUTHOR>
     */
    public void deleteIndustryEnterprise(List<String> ids) {
        for (String id : ids) {
            IndustryEnterprise industryEnterprise = industryEnterpriseMapper.selectByPrimaryKey(id);
            if (industryEnterprise == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            IndustryEnterprise temindustryEnterprise = new IndustryEnterprise();
            temindustryEnterprise.setYn(CommonConstant.FLAG_NO);
            temindustryEnterprise.setId(industryEnterprise.getId());
            industryEnterpriseMapper.updateByPrimaryKeySelective(temindustryEnterprise);
        }
    }

    /**
     * 查询企业导入详情
     *
     * @param id
     * @return IndustryEnterpriseVo
     * <AUTHOR>
     */
    @Override
    public IndustryEnterpriseVo findById(String id) {
        IndustryEnterprise enterprise = industryEnterpriseMapper.selectByPrimaryKey(id);
        if (enterprise == null) {
            return null;
        }

        IndustryEnterpriseVo vo = new IndustryEnterpriseVo();
        BeanUtils.copyProperties(enterprise, vo);

        // 查询关联的机构信息
        Example orgExample = new Example(IndustryEnterpriseOrg.class);
        orgExample.createCriteria()
                .andEqualTo("enterpriseId", id)
                .andEqualTo("yn", CommonConstant.FLAG_YES);
        List<IndustryEnterpriseOrg> orgList = industryEnterpriseOrgMapper.selectByExample(orgExample);

        if (!CollectionUtils.isEmpty(orgList)) {
            List<IndustryEnterpriseVo.orgList> voOrgList = new ArrayList<>();
            for (IndustryEnterpriseOrg org : orgList) {
                IndustryEnterpriseVo.orgList orgItem = new IndustryEnterpriseVo.orgList();
                orgItem.setOrgCode(org.getOrgCode());
                orgItem.setOrgId(org.getUserId());
                voOrgList.add(orgItem);
            }
            vo.setOrgList(voOrgList);
        }

        return vo;
    }


    /**
     * 分页查询企业导入
     *
     * @param vo
     * @return PageInfo<IndustryEnterprise>
     * <AUTHOR>
     */
    @Override
    public PageInfo<IndustryEnterprise> findPageByQuery(IndustryEnterpriseVo vo) {
        if (vo.getEnterpriseType() == null) {
            throw new ServiceException("类型不正确");
        }
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        orderBy(vo.getOrderColumn() + " " + vo.getOrderValue());
        Example example = getExample(vo);
        List<IndustryEnterprise> industryEnterpriseList = industryEnterpriseMapper.selectByExample(example);
        return new PageInfo<IndustryEnterprise>(industryEnterpriseList);
    }

    private Example getExample(IndustryEnterpriseVo vo) {
        Example example = new Example(IndustryEnterprise.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        if (!StringUtils.isEmpty(vo.getEnterpriseType())) {
            criteria.andEqualTo("enterpriseType", vo.getEnterpriseType());
        }

        // 检查用户角色权限
        checkUserRolePermission(criteria);

        // 统一社会信用代码模糊查询
        if (!StringUtils.isEmpty(vo.getUscc())) {
            criteria.andLike("uscc", "%" + vo.getUscc() + "%");
        }

        // 企业名称模糊查询
        if (!StringUtils.isEmpty(vo.getEnterpriseName())) {
            criteria.andLike("enterpriseName", "%" + vo.getEnterpriseName() + "%");
        }

        // 行政区划代码查询
        if (!StringUtils.isEmpty(vo.getAreaCode())) {
            // 如果areaCode以00结尾，表示查询地级市下的所有数据，使用前4位进行模糊查询
            if (vo.getAreaCode().endsWith("00") && vo.getAreaCode().length() >= 6) {
                String cityCode = vo.getAreaCode().substring(0, 4);
                criteria.andLike("areaCode", cityCode + "%");
            } else {
                // 否则进行精确查询
                criteria.andEqualTo("areaCode", vo.getAreaCode());
            }
        }

        // 帮扶时间精确查询
        if (!StringUtils.isEmpty(vo.getHelpYear())) {
            criteria.andEqualTo("helpYear", vo.getHelpYear());
        }

        example.orderBy("createTime").desc();
        return example;
    }

    /**
     * 检查用户角色权限
     *
     * @param criteria 查询条件
     */
    private void checkUserRolePermission(Criteria criteria) {
        // 获取当前用户ID
        String userId = AppUserUtil.getCurrentUserId();
        
        // 获取当前用户角色列表
        List<SysRole> roleList = sysRoleMapper.listByUserId(userId);
        
        // 判断用户角色
        boolean hasProvincialRole = false;
        boolean hasCityRole = false;
        boolean hasCountyRole = false;
        boolean hasOrgRole = false;
        
        // 检查用户角色
        if (roleList != null && !roleList.isEmpty()) {
            for (SysRole role : roleList) {
                String roleCode = role.getRoleCode();
                if (RoleConstant.ROLE_PROVINCIAL.equals(roleCode) || RoleConstant.ROLE_SUPERADMIN.equals(roleCode)) {
                    hasProvincialRole = true;
                } else if (RoleConstant.ROLE_CITY.equals(roleCode)) {
                    hasCityRole = true;
                } else if (RoleConstant.ROLE_COUNTY.equals(roleCode)) {
                    hasCountyRole = true;
                } else if (RoleConstant.ROLE_ORG_ADMIN.equals(roleCode)) {
                    hasOrgRole = true;
                }
            }
        }
        
        // 机构角色不允许查看数据
        if (hasOrgRole && !hasProvincialRole && !hasCityRole && !hasCountyRole) {
            throw new ServiceException("机构角色无权限查看数据");
        }
        
        // 如果是省级角色，可以查看所有数据，不添加额外条件
        if (hasProvincialRole) {
            return;
        }
        
        // 获取用户信息
        SysUser user = new SysUser();
        user.setId(userId);
        SysUser currentUser = sysUserMapper.selectOne(user);
        
        if (currentUser != null && currentUser.getAreaCode() != null) {
            String areaCode = currentUser.getAreaCode();
            
            if (hasCityRole) {
                // 市级角色：获取用户的areaCode，去掉末尾的00，进行模糊匹配
                if (areaCode.endsWith("00")) {
                    areaCode = areaCode.substring(0, areaCode.length() - 2);
                }
                criteria.andLike("areaCode", areaCode + "%");
            } else if (hasCountyRole) {
                // 区县角色：精确匹配areaCode
                criteria.andEqualTo("areaCode", areaCode);
            }
        }
    }

    /**
     * 按条件导出查询企业导入
     *
     * @param industryEnterpriseVo
     * @return PageInfo<IndustryEnterprise>
     * <AUTHOR>
     */
    @Override
    public List<IndustryEnterprise> findByQuery(IndustryEnterpriseVo industryEnterpriseVo) {
        orderBy(industryEnterpriseVo.getOrderColumn() + " " + industryEnterpriseVo.getOrderValue());
        Example example = getExample(industryEnterpriseVo);
        return industryEnterpriseMapper.selectByExample(example);
    }

    /**
     * 导入企业导入
     *
     * @param file
     * @return
     * <AUTHOR>
     */
    @Override
    public void importIndustryEnterpriseAsync(MultipartFile file) {
        //为后续数据处理线程设置用户信息
        Object user = AppUserUtil.getLoginAppUser();
        LoginUser appLoginUser = (LoginUser) user;
        importService.notification(appLoginUser, "已创建企业导入导入任务，完成后会通知", ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "企业导入", null);
        // 开始时间
        Date startTime = new Date();
        // 验证excel
        importService.verifyExcel(file);
        List<IndustryEnterpriseExcel> list;
        // 读取数据
        try (InputStream inputStream = file.getInputStream()) {
            list = (List<IndustryEnterpriseExcel>) EasyExcelUtils.readExcel(IndustryEnterpriseExcel.class, inputStream);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("模板文件为空或者异常，请使用提供的模板进行导入");
            }
        } catch (IOException e) {
            throw new ServiceException("请检查填写内容");
        }
        // 复制file 文件
        String pathForSave = tempPath + "/" + UUIDUtils.getUUID() + "&&" + file.getOriginalFilename();
        File copyFile = FileUtil.copyFile(file, pathForSave);
        MultipartFile copyMultipartFile = FileConvertUtils.createFileItem(copyFile);
        //这里写数据处理逻辑
        CompletableFuture.runAsync(() -> {
            try {
                // 设置用户信息 避免异步处理数据时丢失用户信息
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(appLoginUser, null, appLoginUser.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);

                List<IndustryEnterprise> insertList = new ArrayList<>();
                List<String> errorMessages = new ArrayList<>();

                // 第一步：数据验证和转换
                for (int i = 0; i < list.size(); i++) {
                    IndustryEnterpriseExcel item = list.get(i);
                    StringBuilder errorMsg = new StringBuilder();

                    try {
                        // 数据验证
                        if (StringUtils.isEmpty(item.getCity())) {
                            errorMsg.append("市为空;");
                        }
                        if (StringUtils.isEmpty(item.getCounty())) {
                            errorMsg.append("县(市、区)为空;");
                        }
                        if (StringUtils.isEmpty(item.getMajorCategory())) {
                            errorMsg.append("行业大类为空;");
                        }
                        if (StringUtils.isEmpty(item.getMinorCategory())) {
                            errorMsg.append("行业小类为空;");
                        }
                        // 验证Excel中的企业类型并转换为数字
                        Integer excelEnterpriseType = null;
                        if (!StringUtils.isEmpty(item.getEnterpriseType())) {
                            if ("体系导入企业".equals(item.getEnterpriseType())) {
                                excelEnterpriseType = 1;
                            } else if ("培育类企业".equals(item.getEnterpriseType())) {
                                excelEnterpriseType = 2;
                            } else {
                                errorMsg.append("企业类型不合法，请填写'体系导入企业'或'培育类企业';");
                            }
                        } else {
                            errorMsg.append("企业类型为空;");
                        }
                        if (StringUtils.isEmpty(item.getEnterpriseName())) {
                            errorMsg.append("企业名称为空;");
                        }
                        if (StringUtils.isEmpty(item.getUscc())) {
                            errorMsg.append("统一社会信用代码为空;");
                        }
                        if (StringUtils.isEmpty(item.getAddress())) {
                            errorMsg.append("企业地址为空;");
                        }
                        if (StringUtils.isEmpty(item.getHelpYear())) {
                            errorMsg.append("帮扶时间为空;");
                        }

                        // 验证行业大类是否存在
                        if (!StringUtils.isEmpty(item.getMajorCategory())) {
                            if (!isValidMajorCategory(item.getMajorCategory())) {
                                errorMsg.append("行业大类不存在;");
                            }
                        }

                        // 获取行政区划代码
                        String cityCode = null;
                        String countyCode = null;
                        if (!StringUtils.isEmpty(item.getCity())) {
                            cityCode = getCityCodeByName(item.getCity());
                            if (StringUtils.isEmpty(cityCode)) {
                                errorMsg.append("市名称不存在;");
                            }
                        }
                        if (!StringUtils.isEmpty(item.getCounty()) && !StringUtils.isEmpty(cityCode)) {
                            countyCode = getCountyCodeByName(item.getCounty(), cityCode);
                            if (StringUtils.isEmpty(countyCode)) {
                                errorMsg.append("县(市、区)名称不存在;");
                            }
                        }

                        // 如果有验证错误，记录错误信息并跳过
                        if (errorMsg.length() > 0) {
                            item.setImportSituation(ImportStatusEnum.ERROR.getName() + ":" + errorMsg.toString());
                            errorMessages.add("第" + (i + 1) + "行:" + errorMsg.toString());
                            continue;
                        }

                        // 数据转换
                        IndustryEnterprise industryEnterprise = new IndustryEnterprise();
                        BeanUtils.copyProperties(item, industryEnterprise);
                        
                        // 设置转换后的企业类型数字值
                        if (excelEnterpriseType != null) {
                            industryEnterprise.setEnterpriseType(excelEnterpriseType);
                        }
                        industryEnterprise.setId(UUIDUtils.getUUID());
                        industryEnterprise.setYn(CommonConstant.FLAG_YES);

                        // 设置行政区划代码和名称
                        if (!StringUtils.isEmpty(countyCode)) {
                            industryEnterprise.setAreaCode(countyCode);
                            industryEnterprise.setAreaName(item.getCity() + item.getCounty());
                        }

                        industryEnterprise.setCreateUser(AppUserUtil.getCurrentUserName());
                        industryEnterprise.setCreateUserNickname(AppUserUtil.getCurrentRealName());
                        industryEnterprise.setUpdateUser(AppUserUtil.getCurrentUserName());
                        industryEnterprise.setUpdateUserNickname(AppUserUtil.getCurrentRealName());
                        industryEnterprise.setCreateTime(new Date());
                        industryEnterprise.setUpdateTime(new Date());
                        // 根据企业类型设置企业类型名称
                        if (industryEnterprise.getEnterpriseType() != null) {
                            if (industryEnterprise.getEnterpriseType() == 1) {
                                industryEnterprise.setEnterpriseTypeName("体系导入企业");
                            } else if (industryEnterprise.getEnterpriseType() == 2) {
                                industryEnterprise.setEnterpriseTypeName("培育类企业");
                            }
                        }

                        insertList.add(industryEnterprise);
                        item.setImportSituation("待处理");

                    } catch (Exception e) {
                        item.setImportSituation(ImportStatusEnum.ERROR.getName() + ":" + e.getMessage());
                        errorMessages.add("第" + (i + 1) + "行处理异常:" + e.getMessage());
                        continue;
                    }
                }

                // 如果所有数据都有错误，直接返回
                if (insertList.isEmpty()) {
                    String errorSummary = "存在" + errorMessages.size() + "条数据错误，本次操作将不会存储任何数据。详情：" + String.join("，", errorMessages);
                    importService.notification(appLoginUser, errorSummary, ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "企业导入失败", null);
                    return;
                }

                // 第二步：重复性检查
                List<IndustryEnterprise> finalInsertList = new ArrayList<>();
                int validDataIndex = 0;
                for (int i = 0; i < list.size(); i++) {
                    IndustryEnterpriseExcel excelItem = list.get(i);

                    // 跳过验证失败的数据
                    if (!"待处理".equals(excelItem.getImportSituation())) {
                        continue;
                    }

                    IndustryEnterprise enterprise = insertList.get(validDataIndex);
                    validDataIndex++;

                    // 检查数据库中是否已存在相同的企业（在当前用户的数据中检查）
                    Example checkExample = new Example(IndustryEnterprise.class);
                    checkExample.createCriteria()
                            .andEqualTo("yn", CommonConstant.FLAG_YES)
                            .andEqualTo("enterpriseName", enterprise.getEnterpriseName())
                            .andEqualTo("uscc", enterprise.getUscc())
                            .andEqualTo("enterpriseType", enterprise.getEnterpriseType())
                            .andEqualTo("county", enterprise.getCounty());


                    List<IndustryEnterprise> existingList = industryEnterpriseMapper.selectByExample(checkExample);

                    if (!CollectionUtils.isEmpty(existingList)) {
                        // 发现重复数据，直接跳过
                        excelItem.setImportSituation("跳过：" + enterprise.getEnterpriseName() + "已存在");
                    } else {
                        finalInsertList.add(enterprise);
                        excelItem.setImportSituation(ImportStatusEnum.IMPORT_SUCCESS.getName());
                    }
                }

                // 第三步：批量保存数据
                if (!CollectionUtils.isEmpty(finalInsertList)) {
                    industryEnterpriseMapper.insertList(finalInsertList);
                }

                // 生成导入结果文件
                String fileUrl = importService.faultDataAsync(list, IndustryEnterpriseExcel.class, "导入结果.xls",
                        "企业导入", "企业导入", startTime, copyMultipartFile, appLoginUser);

                String successMsg = "成功导入" + finalInsertList.size() + "条数据";
                if (!errorMessages.isEmpty()) {
                    successMsg += "，" + errorMessages.size() + "条数据存在错误被忽略";
                }

                importService.notification(appLoginUser, "企业导入-导入结果.xls", ExportRespAnnotation.SocketMessageTypeEnum.DOWN, successMsg, fileUrl);

            } catch (Exception e) {
                logger.error("企业导入异常", e);
                importService.notification(appLoginUser, e.getMessage(), ExportRespAnnotation.SocketMessageTypeEnum.NORMAL, "企业导入导入【出现异常】", null);
            } finally {
                try {
                    Files.deleteIfExists(Paths.get(pathForSave));
                } catch (Exception e) {
                    logger.error("删除文件失败", e);
                }
            }
        });
    }

    @Override
    public PageInfo<IndustryEnterprise> enterpriseList(IndustryEnterpriseVo vo) {
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        orderBy(vo.getOrderColumn() + " " + vo.getOrderValue());
        Example example = getExample(vo);
        List<IndustryEnterprise> industryEnterpriseList = industryEnterpriseMapper.selectByExample(example);
        return new PageInfo<IndustryEnterprise>(industryEnterpriseList);
    }

    /**
     * 通过城市名称获取城市代码
     * @param cityName 城市名称
     * @return 城市代码
     */
    private String getCityCodeByName(String cityName) {
        if (StringUtils.isEmpty(cityName)) {
            return null;
        }
        try {
            return basicAreaMapper.getCityCodeByName(cityName);
        } catch (Exception e) {
            logger.error("查询城市代码失败: cityName={}", cityName, e);
            return null;
        }
    }

    /**
     * 通过区县名称和城市代码获取区县代码
     * @param countyName 区县名称
     * @param cityCode 城市代码
     * @return 区县代码
     */
    private String getCountyCodeByName(String countyName, String cityCode) {
        if (StringUtils.isEmpty(countyName) || StringUtils.isEmpty(cityCode)) {
            return null;
        }
        try {
            return basicAreaMapper.getCountyCodeByName(countyName, cityCode);
        } catch (Exception e) {
            logger.error("查询区县代码失败: countyName={}, cityCode={}", countyName, cityCode, e);
            return null;
        }
    }

    /**
     * 验证行业大类是否存在
     * @param majorCategory 行业大类名称
     * @return 是否存在
     */
    private boolean isValidMajorCategory(String majorCategory) {
        if (StringUtils.isEmpty(majorCategory)) {
            return false;
        }
        try {
            int count = basicIndustryMapper.countMajorCategoryByName(majorCategory);
            return count > 0;
        } catch (Exception e) {
            logger.error("验证行业大类失败: majorCategory={}", majorCategory, e);
            return false;
        }
    }
}
