package com.jh.sme.bean.industry;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 清单和企业关联对象 industry_list_enterprise
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Table(name = "industry_list_enterprise")
@Schema(description = "清单和企业关联")
@Data
public class IndustryListEnterprise extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "ENTERPRISE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业导入表ID")
    private String enterpriseId;
    @Column(name = "LIST_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "清单ID")
    private String listId;

}
