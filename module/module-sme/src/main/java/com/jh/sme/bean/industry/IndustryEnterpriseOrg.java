package com.jh.sme.bean.industry;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jh.common.bean.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import jakarta.persistence.*;

/**
 * 企业机构帮扶关联对象 industry_enterprise_org
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Table(name = "industry_enterprise_org")
@Schema(description = "企业机构帮扶关联")
@Data
public class IndustryEnterpriseOrg extends BaseEntity {
    private static final long serialVersionUID = 1L;


    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构用户ID")
    private String userId;
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "机构代码")
    private String orgCode;
    @Column(name = "ENTERPRISE_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "企业ID")
    private String enterpriseId;

}
