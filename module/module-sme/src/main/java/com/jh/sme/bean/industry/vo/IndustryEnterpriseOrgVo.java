package com.jh.sme.bean.industry.vo;


import com.jh.sme.bean.industry.IndustryEnterpriseOrg;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 企业机构帮扶关联Vo
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Schema(description = "IndustryEnterpriseOrgVo")
public class IndustryEnterpriseOrgVo extends IndustryEnterpriseOrg {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}