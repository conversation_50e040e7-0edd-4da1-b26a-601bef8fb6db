package com.jh.sme.bean.basic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 市场监督管理局查询Vo
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Schema(description = "市场监督管理局查询Vo")
@Data
public class MarketSupervisionVo {

    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    @Schema(description = "排序列")
    private String orderColumn = "code";

    @Schema(description = "排序方式")
    private String orderValue = "asc";

    @Schema(description = "城市代码")
    private String cityCode;

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构代码")
    private String code;
}