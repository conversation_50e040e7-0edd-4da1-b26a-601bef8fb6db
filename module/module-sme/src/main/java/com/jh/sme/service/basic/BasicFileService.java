package com.jh.sme.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jh.sme.bean.basic.BasicFile;
import com.jh.sme.bean.basic.vo.BasicFileVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件管理附件Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface BasicFileService {
    /**
     * 保存或更新文件管理附件
     *
     * @param basicFile 文件管理附件对象
     * @return String 文件管理附件ID
     * <AUTHOR>
     */
    String saveOrUpdateBasicFile(BasicFile basicFile);

    /**
     * 删除文件管理附件
     *
     * @param ids void 文件管理附件ID
     * <AUTHOR>
     */
    void deleteBasicFile(List<String> ids);

    /**
     * 查询文件管理附件详情
     *
     * @param id
     * @return BasicFile
     * <AUTHOR>
     */
    BasicFile findById(String id);

    /**
     * 分页查询文件管理附件
     *
     * @param basicFileVo
     * @return PageInfo<BasicFile>
     * <AUTHOR>
     */
    PageInfo<BasicFile> findPageByQuery(BasicFileVo basicFileVo);

}
