package com.jh.sme.dao.basic;

import com.jh.common.mybatis.BaseInfoMapper;
import com.jh.sme.bean.basic.BasicArea;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 行业基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Mapper
public interface BasicAreaMapper extends BaseInfoMapper<BasicArea> {

    /**
     * 查询省级数据
     *
     * @return List<BasicArea>
     */
    List<BasicArea> findProvinceList();

    /**
     * 查询市级数据
     *
     * @return List<BasicArea>
     */
    List<BasicArea> findCityList();

    /**
     * 查询区县级数据
     *
     * @return List<BasicArea>
     */
    List<BasicArea> findCountyList();

    /**
     * 根据父级代码查询下级区划
     *
     * @param parentCode 父级代码
     * @return List<BasicArea>
     */
    List<BasicArea> findByParentCode(@Param("parentCode") String parentCode);

    /**
     * 根据级别查询区划
     *
     * @param level 级别
     * @return List<BasicArea>
     */
    List<BasicArea> findByLevel(@Param("level") Long level);

    /**
     * 根据城市名称获取城市代码
     *
     * @param cityName 城市名称
     * @return 城市代码
     */
    String getCityCodeByName(@Param("cityName") String cityName);

    /**
     * 根据区县名称和城市代码获取区县代码
     *
     * @param countyName 区县名称
     * @param cityCode 城市代码
     * @return 区县代码
     */
    String getCountyCodeByName(@Param("countyName") String countyName, @Param("cityCode") String cityCode);

    /**
     * 查询市场监督管理局列表
     *
     * @param cityCode 城市代码（可选）
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> findMarketSupervisionList(@Param("cityCode") String cityCode);
}
