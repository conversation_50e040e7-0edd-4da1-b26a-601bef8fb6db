package com.jh.sme.controller.word;

import com.github.pagehelper.PageInfo;
import com.jh.common.annotation.RepeatSubAnnotation;
import com.jh.common.annotation.SystemLogAnnotation;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.controller.BaseController;
import com.jh.sme.bean.word.WorkPublicize;
import com.jh.sme.bean.word.vo.WorkPublicizeVo;
import com.jh.sme.service.word.WorkPublicizeService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 宣传报道Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/word/publicize")
@Tag(name = "宣传报道")
public class WorkPublicizeController extends BaseController {
    @Autowired
    private WorkPublicizeService workPublicizeService;

    private static final String PER_PREFIX = "btn:word:publicize:";

    /**
     * 新增宣传报道
     *
     * @param workPublicizeVo 宣传报道数据 json
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增宣传报道")
    @SystemLogAnnotation(type = "宣传报道", value = "新增宣传报道")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveWorkPublicize(@RequestBody WorkPublicizeVo workPublicizeVo) {
        String id = workPublicizeService.saveOrUpdateWorkPublicize(workPublicizeVo);
        return RestApiResponse.ok(id);
    }


    /**
     * 批量删除宣传报道(判断 关联数据是否可以删除)
     *
     * @param ids
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @Operation(summary = "批量删除宣传报道")
    @SystemLogAnnotation(type = "宣传报道", value = "批量删除宣传报道")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteWorkPublicize(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        workPublicizeService.deleteWorkPublicize(ids);
        return RestApiResponse.ok();
    }

    /**
     * 查询宣传报道详情
     *
     * @param id
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @GetMapping("/findById")
    @Operation(summary = "查询宣传报道详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        WorkPublicizeVo workPublicizeVo = workPublicizeService.findById(id);
        return RestApiResponse.ok(workPublicizeVo);
    }

    /**
     * 分页查询宣传报道
     *
     * @param workPublicizeVo 宣传报道 查询条件
     * @return RestApiResponse<?>
     * <AUTHOR>
     */
    @PostMapping("/findPageByQuery")
    @Operation(summary = "分页查询宣传报道")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody WorkPublicizeVo workPublicizeVo) {
        PageInfo<WorkPublicize> workPublicize = workPublicizeService.findPageByQuery(workPublicizeVo);
        return RestApiResponse.ok(workPublicize);
    }

    /**
     * 查询所有宣传报道列表
     *
     * @return RestApiResponse<?>
     */
    @GetMapping("/findList")
    @Operation(summary = "查询所有宣传报道列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findList')")
    public RestApiResponse<?> findList() {
        List<WorkPublicize> list = workPublicizeService.findList();
        return RestApiResponse.ok(list);
    }
}