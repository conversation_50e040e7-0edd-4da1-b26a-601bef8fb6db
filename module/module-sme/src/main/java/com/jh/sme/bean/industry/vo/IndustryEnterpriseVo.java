package com.jh.sme.bean.industry.vo;


import com.jh.sme.bean.industry.IndustryEnterprise;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * 企业导入Vo
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Schema(description = "IndustryEnterpriseVo")
@Data
public class IndustryEnterpriseVo extends IndustryEnterprise {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    private List<orgList> orgList;

    @Data
    public static class orgList {
        private String orgCode;
        private String orgId;
    }

}