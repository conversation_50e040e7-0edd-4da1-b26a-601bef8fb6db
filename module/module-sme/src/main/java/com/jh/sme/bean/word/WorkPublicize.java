package com.jh.sme.bean.word;

import com.jh.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Table;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import java.util.Date;

/**
 * 宣传报道对象 work_publicize
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Table(name = "work_publicize")
@Schema(description = "宣传报道")
@Data
public class WorkPublicize extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Column(name = "REPORT_UNIT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "报送单位")
    private String reportUnit;

    @Column(name = "MEDIA")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "媒体")
    private String media;

    @Column(name = "PUBLICITY_THEME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "宣传主题")
    private String publicityTheme;

    @Column(name = "PUBLISH_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @Schema(description = "发布时间")
    private Date publishTime;

    @Column(name = "PAGE_LAYOUT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "版面")
    private String pageLayout;

    @Column(name = "LINK_URL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "链接")
    private String linkUrl;

    @Column(name = "AREA_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划代码")
    private String areaCode;

    @Column(name = "AREA_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Schema(description = "行政区划名称")
    private String areaName;

}