package com.jh.sme.bean.basic.vo;

import com.jh.sme.bean.basic.BasicIndustry;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * 行业Vo
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Schema(description = "BasicIndustryVo")
@Data
public class BasicIndustryVo extends BasicIndustry {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;



}