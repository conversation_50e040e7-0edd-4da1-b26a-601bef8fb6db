package com.jh.ws.bean;

import jakarta.websocket.Session;

import java.io.Serializable;

public class UserInfo implements Serializable {

	private static final long serialVersionUID = -1379274258881257107L;

	private String token;

	private String userId;

	private String userName;

	private String userNickName;

	private Session session;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserNickName() {
		return userNickName;
	}

	public void setUserNickName(String userNickName) {
		this.userNickName = userNickName;
	}

	public Session getSession() {
		return session;
	}

	public void setSession(Session session) {
		this.session = session;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

}
