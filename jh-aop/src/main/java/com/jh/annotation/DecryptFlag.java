package com.jh.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 脱敏标记字段
 * 如果需要反脱敏，需要在字段上添加该注解，并且这个注解下要有一个Map类型的属性用来存储解敏字段名和解码秘钥
 * 例如：
 * @DecryptFlag
 * private Map<String, String> decryptMap;
 *
 * decryptMap.put("name", "uuid");
 * 这个会返回到页面上可以调用接口（/sys/decrypt/getByKey）进行解密
 */
@Target(value = {ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DecryptFlag {
}
