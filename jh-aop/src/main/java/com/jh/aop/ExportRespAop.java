package com.jh.aop;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import com.jh.common.annotation.ExportRespAnnotation;
import com.jh.common.bean.ExcelMessageInfo;
import com.jh.common.bean.LoginUser;
import com.jh.common.bean.RestApiResponse;
import com.jh.common.bean.SysFileInfo;
import com.jh.common.exception.ServiceException;
import com.jh.common.util.AppUserUtil;
import com.jh.common.util.DateUtils;
import com.jh.common.util.io.FileConvertUtils;
import com.jh.common.util.poi.EasyExcelUtils;
import com.jh.constant.RedisConstant;
import com.jh.sys.bean.SysDown;
import com.jh.sys.bean.vo.DataFiled;
import com.jh.sys.service.FileService;
import com.jh.sys.service.SysDownService;
import com.jh.sys.service.SysTableSetService;
import com.jh.sys.service.impl.FileServiceFactory;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;


@Component
@Aspect
public class ExportRespAop {

    private static final Logger logger = LoggerFactory.getLogger(ExportRespAop.class);

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Autowired
    private SysDownService sysDownServiceImpl;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private FileServiceFactory fileServiceFactory;

    @Autowired
    private SysTableSetService sysTableSetService;

    @Value("${file.useFileType}")
    private String fileSource;

    public static String getCurrentUserId() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser) user;
            return appLoginUser.getId();
        }
        return null;
    }

    @Around(value = "@annotation(com.jh.common.annotation.ExportRespAnnotation)")
    public Object execute(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        ExportRespAnnotation exportAnnotation =
                methodSignature.getMethod().getDeclaredAnnotation(ExportRespAnnotation.class);
        SysDown down = new SysDown();
        down.setUserId(getCurrentUserId());
        down.setDownNum(0);
        down.setDownTime(new Date());
        down.setDownType(exportAnnotation.downType().getName());
        down.setStatus(ExportRespAnnotation.DownStatusEnum.PROGRESS.getStatus());
        down.setModType(exportAnnotation.modType());
        down.setSonMod(exportAnnotation.sonMod());
        down.setRedme(exportAnnotation.modType() + "==>" + exportAnnotation.sonMod() + "  时间:" + DateUtils.getCurrentTimeStr());
        if (exportAnnotation.isNotice()) {
            ExcelMessageInfo wsMsg = new ExcelMessageInfo();
            wsMsg.setSendTime(new Date());
            wsMsg.setMessageType(ExportRespAnnotation.SocketMessageTypeEnum.NORMAL.getValue());
            wsMsg.setMessageTitle(exportAnnotation.sonMod() + ExportRespAnnotation.SocketMessageTypeEnum.DOWN.getName());
            wsMsg.setSendUserId("0");
            wsMsg.setDestUserId(getCurrentUserId());
            wsMsg.setSendContent("已创建下载任务下载完成后会通知");
            redisTemplate.convertAndSend(RedisConstant.REDIS_TOPIC_WEBSOCKET, JSONObject.toJSONString(wsMsg));
        }
        String id = sysDownServiceImpl.saveOrUpdateSysDown(down);
        // 执行目标方法
        Object result = joinPoint.proceed();
        RestApiResponse restApiResponse = JSONObject.parseObject(JSONObject.toJSONString(result), RestApiResponse.class);
        Object respList = restApiResponse.getData();
        List<Object> list = Optional.ofNullable(respList)
                .filter(List.class::isInstance)
                .map(List.class::cast)
                .orElse(Collections.emptyList());
        CompletableFuture.runAsync(() -> {
            SysDown updown = new SysDown();
            try {
                updown.setId(id);
                LinkedHashMap<String, String> head = new LinkedHashMap<>();
                //通过key获取excel导出字段
                ExcelMessageInfo wsMsg = new ExcelMessageInfo();
                try {
                    List<DataFiled> exportColumns = sysTableSetService.getExportColumns(exportAnnotation.key(), down.getUserId());
                    for (DataFiled columns : exportColumns) {
                        head.put(columns.getKey(), columns.getName());
                    }
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();

                    EasyExcelUtils.dynamicExportExcel(list, head, exportAnnotation.fileName(), bos);
                    MultipartFile file = FileConvertUtils.convertToMultipartFile(bos, exportAnnotation.fileName() + ".xlsx");
                    FileService fileService = fileServiceFactory.getFileService(fileSource);
                    //查询当前用户的组织信息 szx增加
                    SysFileInfo fileInfo = new SysFileInfo();
                    fileInfo = fileService.upload(file, fileInfo, "exportExcel");
                    wsMsg.setFileUrl(fileInfo.getUrl());
                    updown.setFileUrl(fileInfo.getUrl());
                    wsMsg.setMessageType(ExportRespAnnotation.SocketMessageTypeEnum.DOWN.getValue());
                    wsMsg.setMessageTitle(exportAnnotation.sonMod() + ExportRespAnnotation.SocketMessageTypeEnum.DOWN.getName() + "成功");
                    wsMsg.setSendContent(exportAnnotation.fileName());

                } catch (ServiceException serviceException) {
                    wsMsg.setMessageType(ExportRespAnnotation.SocketMessageTypeEnum.NORMAL.getValue());
                    wsMsg.setMessageTitle(exportAnnotation.sonMod() + ExportRespAnnotation.SocketMessageTypeEnum.DOWN.getName() + "失败");
                    wsMsg.setSendContent(serviceException.getMessage());
                    wsMsg.setMessageLevel("error");

                }
                wsMsg.setSendTime(new Date());
                wsMsg.setSendUserId("0");
                wsMsg.setDestUserId(down.getUserId());
                redisTemplate.convertAndSend(RedisConstant.REDIS_TOPIC_WEBSOCKET, JSONObject.toJSONString(wsMsg));
                updown.setFileName(exportAnnotation.fileName() + ".xlsx");
                updown.setStatus(ExportRespAnnotation.DownStatusEnum.COMMPLETED.getStatus());
                updown.setGreTime(new Date());
                updown.setRedme(down.getRedme() + "\n" + wsMsg.getSendContent() + "\n" + wsMsg.getFileUrl());
            } catch (Exception e) {
                updown.setStatus(ExportRespAnnotation.DownStatusEnum.FAILED.getStatus());
                updown.setRedme(down.getRedme() + "\n" + "导出失败:" + e.getMessage());
                logger.error("导出失败", e);
            }
            sysDownServiceImpl.saveOrUpdateSysDown(updown);
        });
        return RestApiResponse.ok();
    }


    private String getCurrentIp() {
        // 取用户信息和请求路径
        HttpServletRequest request =
                (HttpServletRequest) ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
